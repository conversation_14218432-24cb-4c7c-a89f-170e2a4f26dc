{% extends "base.html" %}

{% block title %}لوحة تحكم مدير المستخدمين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-users-cog me-2"></i>
                    لوحة تحكم مدير المستخدمين
                </h2>
                <div>
                    <a href="{{ url_for('send_notification') }}" class="btn btn-primary me-2">
                        <i class="fas fa-paper-plane me-1"></i>
                        إرسال إشعار
                    </a>
                    <a href="{{ url_for('view_notifications') }}" class="btn btn-outline-info me-2">
                        <i class="fas fa-bell me-1"></i>
                        عرض الإشعارات
                    </a>
                    <a href="{{ url_for('users_list') }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-list me-1"></i>
                        قائمة المستخدمين
                    </a>
                    <span class="text-muted">مرحباً، {{ current_user.username }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ stats.total_users }}</h4>
                            <p class="mb-0">إجمالي المستخدمين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ stats.active_users }}</h4>
                            <p class="mb-0">المستخدمون النشطون</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ stats.inactive_users }}</h4>
                            <p class="mb-0">المستخدمون غير النشطين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-times fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ stats.teachers_count }}</h4>
                            <p class="mb-0">المعلمون</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chalkboard-teacher fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إدارة المستخدمين -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="userTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="all-users-tab" data-bs-toggle="tab"
                                data-bs-target="#all-users" type="button" role="tab">
                                <i class="fas fa-users me-2"></i>جميع المستخدمين
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="teachers-tab" data-bs-toggle="tab" data-bs-target="#teachers"
                                type="button" role="tab">
                                <i class="fas fa-chalkboard-teacher me-2"></i>المعلمون
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="inspectors-tab" data-bs-toggle="tab"
                                data-bs-target="#inspectors" type="button" role="tab">
                                <i class="fas fa-search me-2"></i>المفتشون
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="userTabsContent">
                        <!-- جميع المستخدمين -->
                        <div class="tab-pane fade show active" id="all-users" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>اسم المستخدم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الدور</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for user in all_users %}
                                        <tr>
                                            <td>{{ user.username }}</td>
                                            <td>{{ user.email }}</td>
                                            <td>
                                                <span class="badge bg-secondary">{{ user.role }}</span>
                                            </td>
                                            <td>
                                                {% if user.is_active %}
                                                <span class="badge bg-success">نشط</span>
                                                {% else %}
                                                <span class="badge bg-danger">غير نشط</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('view_user_profile', user_id=user.id) }}"
                                                    class="btn btn-sm btn-outline-info me-1" title="عرض الملف الشخصي">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                {% if user.role != 'admin' %}
                                                {% if user.is_active %}
                                                <form method="POST"
                                                    action="{{ url_for('user_manager_deactivate_user', user_id=user.id) }}"
                                                    style="display: inline;">
                                                    <button type="submit" class="btn btn-sm btn-warning"
                                                        onclick="return confirm('هل أنت متأكد من تعطيل هذا المستخدم؟')"
                                                        title="تعطيل الحساب">
                                                        <i class="fas fa-user-times"></i>
                                                    </button>
                                                </form>
                                                {% else %}
                                                <form method="POST"
                                                    action="{{ url_for('user_manager_activate_user', user_id=user.id) }}"
                                                    style="display: inline;">
                                                    <button type="submit" class="btn btn-sm btn-success"
                                                        title="تفعيل الحساب">
                                                        <i class="fas fa-user-check"></i>
                                                    </button>
                                                </form>
                                                {% endif %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- المعلمون -->
                        <div class="tab-pane fade" id="teachers" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>اسم المستخدم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for teacher in teachers %}
                                        <tr>
                                            <td>{{ teacher.username }}</td>
                                            <td>{{ teacher.email }}</td>
                                            <td>
                                                {% if teacher.is_active %}
                                                <span class="badge bg-success">نشط</span>
                                                {% else %}
                                                <span class="badge bg-danger">غير نشط</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if teacher.is_active %}
                                                <form method="POST"
                                                    action="{{ url_for('user_manager_deactivate_user', user_id=teacher.id) }}"
                                                    style="display: inline;">
                                                    <button type="submit" class="btn btn-sm btn-warning"
                                                        onclick="return confirm('هل أنت متأكد من تعطيل هذا المعلم؟')">
                                                        <i class="fas fa-user-times"></i> تعطيل
                                                    </button>
                                                </form>
                                                {% else %}
                                                <form method="POST"
                                                    action="{{ url_for('user_manager_activate_user', user_id=teacher.id) }}"
                                                    style="display: inline;">
                                                    <button type="submit" class="btn btn-sm btn-success">
                                                        <i class="fas fa-user-check"></i> تفعيل
                                                    </button>
                                                </form>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- المفتشون -->
                        <div class="tab-pane fade" id="inspectors" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>اسم المستخدم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for inspector in inspectors %}
                                        <tr>
                                            <td>{{ inspector.username }}</td>
                                            <td>{{ inspector.email }}</td>
                                            <td>
                                                {% if inspector.is_active %}
                                                <span class="badge bg-success">نشط</span>
                                                {% else %}
                                                <span class="badge bg-danger">غير نشط</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if inspector.is_active %}
                                                <form method="POST"
                                                    action="{{ url_for('user_manager_deactivate_user', user_id=inspector.id) }}"
                                                    style="display: inline;">
                                                    <button type="submit" class="btn btn-sm btn-warning"
                                                        onclick="return confirm('هل أنت متأكد من تعطيل هذا المفتش؟')">
                                                        <i class="fas fa-user-times"></i> تعطيل
                                                    </button>
                                                </form>
                                                {% else %}
                                                <form method="POST"
                                                    action="{{ url_for('user_manager_activate_user', user_id=inspector.id) }}"
                                                    style="display: inline;">
                                                    <button type="submit" class="btn btn-sm btn-success">
                                                        <i class="fas fa-user-check"></i> تفعيل
                                                    </button>
                                                </form>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- رسائل التنبيه -->
{% with messages = get_flashed_messages(with_categories=true) %}
{% if messages %}
<div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
    {% for category, message in messages %}
    <div class="toast show" role="alert">
        <div class="toast-header">
            <strong class="me-auto">إشعار</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            {{ message }}
        </div>
    </div>
    {% endfor %}
</div>
{% endif %}
{% endwith %}
{% endblock %}