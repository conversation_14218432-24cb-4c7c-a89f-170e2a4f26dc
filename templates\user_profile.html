{% extends "base.html" %}

{% block title %}الملف الشخصي - {{ user.username }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-user-circle me-2 text-primary"></i>
                        الملف الشخصي
                    </h2>
                    <p class="text-muted mb-0">عرض تفاصيل المستخدم</p>
                </div>
                <div>
                    <a href="{{ url_for('users_list') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات المستخدم الأساسية -->
    <div class="row">
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <!-- صورة المستخدم -->
                    <div class="mb-3">
                        {% if user.role == 'admin' %}
                        <i class="fas fa-user-shield fa-5x text-danger"></i>
                        {% elif user.role == 'user_manager' %}
                        <i class="fas fa-users-cog fa-5x text-info"></i>
                        {% elif user.role == 'inspector' %}
                        <i class="fas fa-user-tie fa-5x text-warning"></i>
                        {% elif user.role == 'teacher' %}
                        <i class="fas fa-chalkboard-teacher fa-5x text-success"></i>
                        {% else %}
                        <i class="fas fa-user fa-5x text-secondary"></i>
                        {% endif %}
                    </div>

                    <!-- اسم المستخدم -->
                    <h4 class="card-title mb-1">{{ user.username }}</h4>

                    <!-- الدور -->
                    <span class="badge 
                        {% if user.role == 'admin' %}bg-danger
                        {% elif user.role == 'user_manager' %}bg-info
                        {% elif user.role == 'inspector' %}bg-warning
                        {% elif user.role == 'teacher' %}bg-success
                        {% else %}bg-secondary{% endif %} fs-6 mb-3">
                        {% if user.role == 'admin' %}إدارة
                        {% elif user.role == 'user_manager' %}مدير المستخدمين
                        {% elif user.role == 'inspector' %}مفتش
                        {% elif user.role == 'teacher' %}أستاذ
                        {% else %}{{ user.role }}{% endif %}
                    </span>

                    <!-- حالة الحساب -->
                    <div class="mt-3">
                        {% if user.is_active %}
                        <span class="badge bg-success fs-6">
                            <i class="fas fa-check-circle me-1"></i>
                            حساب مفعل
                        </span>
                        {% else %}
                        <span class="badge bg-danger fs-6">
                            <i class="fas fa-times-circle me-1"></i>
                            حساب معطل
                        </span>
                        {% endif %}
                    </div>

                    <!-- أزرار الإجراءات -->
                    {% if (user.role != 'admin') and ((current_user.role == 'admin') or (current_user.role ==
                    'user_manager' and user.role in ['teacher', 'inspector'])) %}
                    <div class="mt-4">
                        {% if user.is_active %}
                        <form method="POST" action="{{ url_for('user_manager_deactivate_user', user_id=user.id) }}"
                            style="display: inline;">
                            <button type="submit" class="btn btn-outline-danger btn-sm"
                                onclick="return confirm('هل أنت متأكد من تعطيل هذا الحساب؟')">
                                <i class="fas fa-user-times me-1"></i>
                                تعطيل الحساب
                            </button>
                        </form>
                        {% else %}
                        <form method="POST" action="{{ url_for('user_manager_activate_user', user_id=user.id) }}"
                            style="display: inline;">
                            <button type="submit" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-user-check me-1"></i>
                                تفعيل الحساب
                            </button>
                        </form>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <!-- تفاصيل الاتصال -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-address-card me-2"></i>
                        معلومات الاتصال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني
                                </label>
                                <div class="form-control-plaintext">{{ user.email }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-phone me-1"></i>
                                    رقم الهاتف
                                </label>
                                <div class="form-control-plaintext">
                                    {% if user.phone_number %}
                                    <span class="phone-number">{{ user.formatted_phone }}</span>
                                    {% else %}
                                    <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    الولاية
                                </label>
                                <div class="form-control-plaintext">
                                    {% if user.wilaya_code %}
                                    {{ user.wilaya_name }}
                                    {% else %}
                                    <span class="text-muted">غير محددة</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    تاريخ التسجيل
                                </label>
                                <div class="form-control-plaintext">
                                    {{ user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else 'غير محدد' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات النشاط -->
            {% if stats %}
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        إحصائيات النشاط
                    </h5>
                </div>
                <div class="card-body">
                    {% if user.role == 'teacher' %}
                    <div class="row text-center">
                        <div class="col-md-6">
                            <div class="border rounded p-3">
                                <i class="fas fa-calendar-check fa-2x text-primary mb-2"></i>
                                <h4 class="mb-1">{{ stats.total_schedules }}</h4>
                                <small class="text-muted">إجمالي الجداول</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="border rounded p-3">
                                <i class="fas fa-tasks fa-2x text-success mb-2"></i>
                                <h4 class="mb-1">{{ stats.total_progress }}</h4>
                                <small class="text-muted">إدخالات التقدم</small>
                            </div>
                        </div>
                    </div>
                    {% elif user.role == 'inspector' %}
                    <div class="row text-center">
                        <div class="col-md-6">
                            <div class="border rounded p-3">
                                <i class="fas fa-users fa-2x text-warning mb-2"></i>
                                <h4 class="mb-1">{{ stats.assigned_teachers }}</h4>
                                <small class="text-muted">الأساتذة المكلفين</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="border rounded p-3">
                                <i class="fas fa-bell fa-2x text-info mb-2"></i>
                                <h4 class="mb-1">{{ stats.total_notifications }}</h4>
                                <small class="text-muted">إجمالي الإشعارات</small>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}