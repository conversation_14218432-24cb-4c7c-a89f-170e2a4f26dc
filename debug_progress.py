#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت تشخيصي لفحص مشكلة عدم تطابق إحصائيات التقدم
"""

from app import app, db
from models_new import ProgressEntry, EducationalLevel, LevelDatabase, LevelDataEntry, User, Role
from sqlalchemy import func

def debug_progress_stats():
    """
    فحص تفصيلي لإحصائيات التقدم
    """
    with app.app_context():
        print("=== تشخيص مشكلة إحصائيات التقدم ===\n")

        # البحث عن المعلمين في النظام
        teachers = User.query.filter_by(role=Role.TEACHER).all()
        print(f"المعلمون في النظام: {len(teachers)}")
        for teacher in teachers:
            print(f"  - {teacher.username} (ID: {teacher.id})")

        if not teachers:
            print("لا يوجد معلمون في النظام!")
            return

        # استخدام أول معلم
        user_id = teachers[0].id
        print(f"\nسيتم فحص المعلم: {teachers[0].username} (ID: {user_id})\n")
        
        # 1. فحص جميع سجلات التقدم للمعلم
        all_progress = ProgressEntry.query.filter_by(user_id=user_id).all()
        print(f"إجمالي سجلات التقدم للمعلم: {len(all_progress)}")
        
        # 2. فحص السجلات المكتملة
        completed_progress = ProgressEntry.query.filter_by(user_id=user_id, status='completed').all()
        print(f"السجلات المكتملة: {len(completed_progress)}")
        
        # 3. فحص السجلات حسب المستوى
        print("\n=== السجلات حسب المستوى ===")
        levels = EducationalLevel.query.all()
        
        for level in levels:
            print(f"\n--- {level.name} (ID: {level.id}) ---")
            
            # السجلات التي تحتوي على level_id مباشرة
            direct_level_progress = ProgressEntry.query.filter_by(
                user_id=user_id, 
                level_id=level.id
            ).all()
            print(f"السجلات مع level_id مباشرة: {len(direct_level_progress)}")
            
            # السجلات المكتملة مع level_id مباشرة
            direct_completed = ProgressEntry.query.filter_by(
                user_id=user_id, 
                level_id=level.id,
                status='completed'
            ).all()
            print(f"السجلات المكتملة مع level_id مباشرة: {len(direct_completed)}")
            
            # فحص السجلات عبر material_id
            level_db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
            if level_db:
                # الحصول على جميع المواد المعرفية للمستوى
                materials = LevelDataEntry.query.filter_by(
                    database_id=level_db.id,
                    entry_type='material',
                    is_active=True
                ).all()
                material_ids = [m.id for m in materials]
                print(f"عدد المواد المعرفية في المستوى: {len(materials)}")
                
                # السجلات التي تحتوي على material_id من هذا المستوى
                material_progress = ProgressEntry.query.filter_by(
                    user_id=user_id
                ).filter(ProgressEntry.material_id.in_(material_ids)).all()
                print(f"السجلات مع material_id من المستوى: {len(material_progress)}")
                
                # السجلات المكتملة مع material_id من هذا المستوى
                material_completed = ProgressEntry.query.filter_by(
                    user_id=user_id,
                    status='completed'
                ).filter(ProgressEntry.material_id.in_(material_ids)).all()
                print(f"السجلات المكتملة مع material_id من المستوى: {len(material_completed)}")
                
                # طباعة تفاصيل السجلات المكتملة
                if material_completed:
                    print("تفاصيل السجلات المكتملة:")
                    for i, entry in enumerate(material_completed):
                        material = LevelDataEntry.query.get(entry.material_id)
                        material_name = material.name if material else "غير معروف"
                        print(f"  {i+1}. ID: {entry.id}, Material: {material_name}, Date: {entry.date}")
        
        # 4. فحص السجلات التي تفتقر إلى معلومات
        print("\n=== السجلات التي تفتقر إلى معلومات ===")
        
        no_level_id = ProgressEntry.query.filter_by(user_id=user_id, level_id=None).all()
        print(f"السجلات بدون level_id: {len(no_level_id)}")
        
        no_material_id = ProgressEntry.query.filter_by(user_id=user_id, material_id=None).all()
        print(f"السجلات بدون material_id: {len(no_material_id)}")
        
        # 5. فحص السجلات المكتملة بدون material_id
        completed_no_material = ProgressEntry.query.filter_by(
            user_id=user_id, 
            status='completed',
            material_id=None
        ).all()
        print(f"السجلات المكتملة بدون material_id: {len(completed_no_material)}")
        
        if completed_no_material:
            print("تفاصيل السجلات المكتملة بدون material_id:")
            for i, entry in enumerate(completed_no_material):
                print(f"  {i+1}. ID: {entry.id}, Level: {entry.level_id}, Subject: {entry.subject_id}, Domain: {entry.domain_id}")

if __name__ == "__main__":
    debug_progress_stats()
