#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح عرض الكفاءات في سجلات التقدم القديمة
"""

from app import app, db
from models_new import ProgressEntry, LevelDataEntry, User, Role
from datetime import date

def fix_missing_competency_ids():
    """
    إصلاح السجلات التي تحتوي على material_id لكن لا تحتوي على competency_id
    """
    with app.app_context():
        print("=== إصلاح السجلات المفقودة competency_id ===\n")
        
        # البحث عن السجلات التي تحتوي على material_id لكن لا تحتوي على competency_id
        entries_to_fix = ProgressEntry.query.filter(
            ProgressEntry.material_id.isnot(None),
            ProgressEntry.competency_id.is_(None)
        ).all()
        
        print(f"عدد السجلات التي تحتاج إصلاح: {len(entries_to_fix)}")
        
        fixed_count = 0
        
        for entry in entries_to_fix:
            try:
                # البحث عن كفاءة مناسبة للمادة المعرفية
                competencies = LevelDataEntry.query.filter_by(
                    parent_id=entry.material_id,
                    entry_type='competency'
                ).all()
                
                if competencies:
                    # اختيار أول كفاءة متاحة
                    selected_competency = competencies[0]
                    entry.competency_id = selected_competency.id
                    
                    user = User.query.get(entry.user_id)
                    username = user.username if user else 'غير معروف'
                    
                    print(f"تم إصلاح السجل {entry.id} للمستخدم {username}")
                    print(f"  - المادة المعرفية: {entry.material_id}")
                    print(f"  - الكفاءة المضافة: {selected_competency.name} (ID: {selected_competency.id})")
                    
                    fixed_count += 1
                else:
                    print(f"⚠️ لم يتم العثور على كفاءات للمادة المعرفية {entry.material_id} في السجل {entry.id}")
                    
            except Exception as e:
                print(f"خطأ في إصلاح السجل {entry.id}: {str(e)}")
        
        if fixed_count > 0:
            try:
                db.session.commit()
                print(f"\n✅ تم إصلاح {fixed_count} سجل بنجاح!")
            except Exception as e:
                db.session.rollback()
                print(f"❌ خطأ في حفظ التغييرات: {str(e)}")
        else:
            print("\n✅ لا توجد سجلات تحتاج إصلاح!")

def verify_all_entries():
    """
    التحقق من جميع السجلات للتأكد من صحة البيانات
    """
    with app.app_context():
        print("\n=== التحقق من جميع السجلات ===\n")
        
        all_entries = ProgressEntry.query.all()
        print(f"إجمالي السجلات: {len(all_entries)}")
        
        # إحصائيات
        with_competency = 0
        with_material = 0
        complete_entries = 0
        incomplete_entries = 0
        
        for entry in all_entries:
            if entry.competency_id:
                with_competency += 1
            if entry.material_id:
                with_material += 1
            if entry.competency_id and entry.material_id:
                complete_entries += 1
            else:
                incomplete_entries += 1
        
        print(f"السجلات التي تحتوي على competency_id: {with_competency}")
        print(f"السجلات التي تحتوي على material_id: {with_material}")
        print(f"السجلات المكتملة (competency_id + material_id): {complete_entries}")
        print(f"السجلات غير المكتملة: {incomplete_entries}")
        
        # عرض السجلات غير المكتملة
        if incomplete_entries > 0:
            print(f"\nالسجلات غير المكتملة:")
            incomplete = ProgressEntry.query.filter(
                db.or_(
                    ProgressEntry.competency_id.is_(None),
                    ProgressEntry.material_id.is_(None)
                )
            ).limit(5).all()
            
            for entry in incomplete:
                user = User.query.get(entry.user_id)
                username = user.username if user else 'غير معروف'
                print(f"  - السجل {entry.id} للمستخدم {username}: competency_id={entry.competency_id}, material_id={entry.material_id}")

def test_user_entries(username):
    """
    اختبار سجلات مستخدم محدد
    """
    with app.app_context():
        print(f"\n=== اختبار سجلات المستخدم {username} ===\n")
        
        user = User.query.filter_by(username=username).first()
        if not user:
            print(f"المستخدم {username} غير موجود!")
            return
        
        entries = ProgressEntry.query.filter_by(user_id=user.id).order_by(ProgressEntry.date.desc()).limit(5).all()
        
        print(f"آخر {len(entries)} سجلات للمستخدم {username}:")
        
        for i, entry in enumerate(entries, 1):
            print(f"\n  {i}. السجل {entry.id}:")
            print(f"     التاريخ: {entry.date}")
            print(f"     الحالة: {entry.status}")
            print(f"     competency_id: {entry.competency_id}")
            print(f"     material_id: {entry.material_id}")
            
            # اختبار الكفاءة
            if entry.competency_id:
                competency = LevelDataEntry.query.filter_by(
                    id=entry.competency_id,
                    entry_type='competency'
                ).first()
                if competency:
                    print(f"     الكفاءة: {competency.name}")
                else:
                    print(f"     ⚠️ الكفاءة غير موجودة!")
            
            # اختبار المادة المعرفية
            if entry.material_id:
                material = LevelDataEntry.query.filter_by(
                    id=entry.material_id,
                    entry_type='material'
                ).first()
                if material:
                    print(f"     المادة المعرفية: {material.name}")
                else:
                    print(f"     ⚠️ المادة المعرفية غير موجودة!")

if __name__ == "__main__":
    print("1. إصلاح السجلات المفقودة...")
    fix_missing_competency_ids()
    
    print("\n" + "="*60)
    print("2. التحقق من جميع السجلات...")
    verify_all_entries()
    
    print("\n" + "="*60)
    print("3. اختبار المستخدم tahar...")
    test_user_entries('tahar')
    
    print("\n" + "="*60)
    print("4. اختبار المستخدم teacher...")
    test_user_entries('teacher')
    
    print("\nتم الانتهاء من الإصلاح والاختبار!")
