/**
 * ملف التأثيرات المتحركة لنظام Ta9affi
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // إضافة تأثيرات للأيقونات المتحركة
    initAnimatedIcons();
    
    // إضافة تأثيرات للبطاقات
    initCardAnimations();
    
    // إضافة تأثيرات للأزرار
    initButtonAnimations();
    
    // إضافة تأثيرات التمرير
    initScrollAnimations();
    
    // إضافة تأثيرات الماوس
    initMouseEffects();
});

/**
 * تهيئة الأيقونات المتحركة
 */
function initAnimatedIcons() {
    const animatedIcons = document.querySelectorAll('.animated-icon');
    
    animatedIcons.forEach(icon => {
        // تأثير التمرير
        icon.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.2) rotate(10deg)';
            this.style.transition = 'all 0.3s ease';
        });
        
        icon.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
        
        // تأثير النقر
        icon.addEventListener('click', function() {
            this.style.animation = 'none';
            setTimeout(() => {
                this.style.animation = '';
            }, 10);
        });
    });
}

/**
 * تهيئة تأثيرات البطاقات
 */
function initCardAnimations() {
    const cards = document.querySelectorAll('.home-card');
    
    cards.forEach((card, index) => {
        // تأثير الظهور المتدرج
        card.style.animationDelay = `${index * 0.2}s`;
        
        // تأثير التمرير
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.home-icon');
            if (icon) {
                icon.style.transform = 'scale(1.3) rotate(360deg)';
                icon.style.transition = 'all 0.5s ease';
            }
            
            // إضافة تأثير الإضاءة
            this.classList.add('glow-effect');
        });
        
        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.home-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
            
            // إزالة تأثير الإضاءة
            this.classList.remove('glow-effect');
        });
    });
}

/**
 * تهيئة تأثيرات الأزرار
 */
function initButtonAnimations() {
    const buttons = document.querySelectorAll('.btn-hover-effect');
    
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            // إضافة تأثير الموجة
            createRippleEffect(this);
        });
    });
}

/**
 * إنشاء تأثير الموجة
 */
function createRippleEffect(element) {
    const ripple = document.createElement('span');
    ripple.classList.add('ripple');
    
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = (rect.width / 2 - size / 2) + 'px';
    ripple.style.top = (rect.height / 2 - size / 2) + 'px';
    
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * تهيئة تأثيرات التمرير
 */
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // مراقبة العناصر القابلة للتحريك
    const animatableElements = document.querySelectorAll('.slide-in, .fade-in');
    animatableElements.forEach(el => observer.observe(el));
}

/**
 * تهيئة تأثيرات الماوس
 */
function initMouseEffects() {
    // تأثير تتبع الماوس للأيقونات الكبيرة
    const largeIcons = document.querySelectorAll('.fa-3x');
    
    largeIcons.forEach(icon => {
        icon.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;
            
            this.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px) scale(1.1)`;
        });
        
        icon.addEventListener('mouseleave', function() {
            this.style.transform = 'translate(0, 0) scale(1)';
        });
    });
}

/**
 * تأثيرات إضافية للنصوص
 */
function initTextAnimations() {
    const titles = document.querySelectorAll('.card-title');
    
    titles.forEach(title => {
        title.addEventListener('mouseenter', function() {
            this.style.textShadow = '2px 2px 4px rgba(25, 118, 210, 0.3)';
            this.style.transition = 'all 0.3s ease';
        });
        
        title.addEventListener('mouseleave', function() {
            this.style.textShadow = 'none';
        });
    });
}

/**
 * تأثير الجسيمات المتحركة (اختياري)
 */
function createParticleEffect() {
    const canvas = document.createElement('canvas');
    canvas.id = 'particles-canvas';
    canvas.style.position = 'fixed';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.pointerEvents = 'none';
    canvas.style.zIndex = '-1';
    
    document.body.appendChild(canvas);
    
    const ctx = canvas.getContext('2d');
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    
    const particles = [];
    const particleCount = 50;
    
    // إنشاء الجسيمات
    for (let i = 0; i < particleCount; i++) {
        particles.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 0.5,
            vy: (Math.random() - 0.5) * 0.5,
            size: Math.random() * 2 + 1,
            opacity: Math.random() * 0.5 + 0.2
        });
    }
    
    // تحريك الجسيمات
    function animateParticles() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        particles.forEach(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            // إعادة تدوير الجسيمات
            if (particle.x < 0) particle.x = canvas.width;
            if (particle.x > canvas.width) particle.x = 0;
            if (particle.y < 0) particle.y = canvas.height;
            if (particle.y > canvas.height) particle.y = 0;
            
            // رسم الجسيمة
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(25, 118, 210, ${particle.opacity})`;
            ctx.fill();
        });
        
        requestAnimationFrame(animateParticles);
    }
    
    animateParticles();
    
    // تحديث حجم الكانفاس عند تغيير حجم النافذة
    window.addEventListener('resize', () => {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    });
}

// تشغيل تأثير الجسيمات (اختياري - يمكن تفعيله حسب الحاجة)
// createParticleEffect();

// إضافة CSS للتأثيرات الإضافية
const additionalStyles = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .animate-in {
        animation: slideInUp 0.8s ease-out;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;

// إضافة الأنماط إلى الصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
