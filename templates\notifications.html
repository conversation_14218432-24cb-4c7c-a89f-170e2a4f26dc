{% extends "base.html" %}

{% block title %}الإشعارات{% endblock %}

{% block extra_css %}
<style>
    .nav-tabs .nav-link {
        color: #495057;
        border: 1px solid transparent;
        border-top-left-radius: 0.375rem;
        border-top-right-radius: 0.375rem;
    }

    .nav-tabs .nav-link:hover {
        border-color: #e9ecef #e9ecef #dee2e6;
        isolation: isolate;
    }

    .nav-tabs .nav-link.active {
        color: #495057;
        background-color: #fff;
        border-color: #dee2e6 #dee2e6 #fff;
    }

    .notification-item {
        transition: all 0.3s ease;
        border-left: 4px solid #28a745;
        /* ضمان عدم إخفاء الإشعارات تلقائياً */
        position: relative !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .notification-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .success-message {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1050;
        min-width: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-bell me-2 text-primary"></i>
            الإشعارات
        </h1>
        <div class="d-flex gap-2">
            {% if current_user.role in ['admin', 'user_manager', 'inspector'] %}
            <a href="{{ url_for('send_notification') }}" class="btn btn-primary">
                <i class="fas fa-paper-plane me-1"></i>
                إرسال إشعار
            </a>
            {% endif %}
            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>
                العودة
            </a>
        </div>
    </div>

    <!-- رسائل النجاح -->
    <div id="success-message" class="alert alert-success alert-dismissible fade success-message" style="display: none;">
        <i class="fas fa-check-circle me-2"></i>
        <span id="success-text"></span>
        <button type="button" class="btn-close" onclick="hideSuccessMessage()"></button>
    </div>

    <!-- التبويبات -->
    <div class="card shadow">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="notificationTabs" role="tablist">
                {% if current_user_role == 'inspector' %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="admin-tab" data-bs-toggle="tab"
                        data-bs-target="#admin-notifications" type="button" role="tab">
                        <i class="fas fa-user-tie me-1"></i>
                        من الإدارة
                        {% if notifications_data.admin_notifications %}
                        <span class="badge bg-primary ms-1">{{ notifications_data.admin_notifications|length }}</span>
                        {% endif %}
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent-notifications"
                        type="button" role="tab">
                        <i class="fas fa-paper-plane me-1"></i>
                        المرسل
                        {% if notifications_data.sent_notifications %}
                        <span class="badge bg-info ms-1">{{ notifications_data.sent_notifications|length }}</span>
                        {% endif %}
                    </button>
                </li>
                {% elif current_user_role == 'teacher' %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="admin-tab" data-bs-toggle="tab"
                        data-bs-target="#admin-notifications" type="button" role="tab">
                        <i class="fas fa-user-tie me-1"></i>
                        من الإدارة
                        {% if notifications_data.admin_notifications %}
                        <span class="badge bg-primary ms-1">{{ notifications_data.admin_notifications|length }}</span>
                        {% endif %}
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="inspector-tab" data-bs-toggle="tab"
                        data-bs-target="#inspector-notifications" type="button" role="tab">
                        <i class="fas fa-user-check me-1"></i>
                        من المفتش
                        {% if notifications_data.inspector_notifications %}
                        <span class="badge bg-success ms-1">{{ notifications_data.inspector_notifications|length
                            }}</span>
                        {% endif %}
                    </button>
                </li>
                {% elif current_user_role == 'admin' %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="sent-tab" data-bs-toggle="tab"
                        data-bs-target="#sent-notifications" type="button" role="tab">
                        <i class="fas fa-paper-plane me-1"></i>
                        المرسل
                        {% if notifications_data.sent_notifications %}
                        <span class="badge bg-info ms-1">{{ notifications_data.sent_notifications|length }}</span>
                        {% endif %}
                    </button>
                </li>
                {% endif %}

                {% if notifications_data.general_notifications %}
                <li class="nav-item" role="presentation">
                    <button
                        class="nav-link {% if current_user_role == 'admin' and not notifications_data.sent_notifications %}active{% endif %}"
                        id="general-tab" data-bs-toggle="tab" data-bs-target="#general-notifications" type="button"
                        role="tab">
                        <i class="fas fa-globe me-1"></i>
                        الإشعارات العامة
                        <span class="badge bg-warning ms-1">{{ notifications_data.general_notifications|length }}</span>
                    </button>
                </li>
                {% endif %}
            </ul>
        </div>

        <div class="card-body">
            <div class="tab-content" id="notificationTabsContent">
                <!-- تبويب الإشعارات من الإدارة -->
                {% if current_user_role in ['inspector', 'teacher'] %}
                <div class="tab-pane fade show active" id="admin-notifications" role="tabpanel">
                    {% if notifications_data.admin_notifications %}
                    {% for notification in notifications_data.admin_notifications %}
                    <div
                        class="alert {% if notification.is_read %}alert-light{% else %}alert-primary{% endif %} notification-item d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <h6 class="mb-0 {% if not notification.is_read %}fw-bold{% endif %}">
                                    {% if not notification.is_read %}
                                    <i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>
                                    {% endif %}
                                    {{ notification.title }}
                                </h6>
                                <span class="badge bg-primary ms-2">من الإدارة</span>
                            </div>
                            <p class="mb-2">{{ notification.message }}</p>
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                من: {{ notification.sender.username }}
                                <span class="mx-2">|</span>
                                <i class="fas fa-clock me-1"></i>
                                {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                        {% if not notification.is_read %}
                        <a href="{{ url_for('mark_notification_read', notification_id=notification.id, notification_type=notification.notification_type) }}"
                            class="btn btn-sm btn-outline-primary" onclick="markAsReadAndUpdate(event, this)">
                            <i class="fas fa-check me-1"></i>
                            تحديد كمقروء
                        </a>
                        {% endif %}
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد إشعارات من الإدارة</p>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- تبويب الإشعارات من المفتش (للأساتذة فقط) -->
                {% if current_user_role == 'teacher' %}
                <div class="tab-pane fade" id="inspector-notifications" role="tabpanel">
                    {% if notifications_data.inspector_notifications %}
                    {% for notification in notifications_data.inspector_notifications %}
                    <div
                        class="alert {% if notification.is_read %}alert-light{% else %}alert-success{% endif %} notification-item d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <h6 class="mb-0 {% if not notification.is_read %}fw-bold{% endif %}">
                                    {% if not notification.is_read %}
                                    <i class="fas fa-circle text-success me-2" style="font-size: 0.5rem;"></i>
                                    {% endif %}
                                    {{ notification.title }}
                                </h6>
                                <span class="badge bg-success ms-2">من المفتش</span>
                            </div>
                            <p class="mb-2">{{ notification.message }}</p>
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                من: {{ notification.sender.username }}
                                <span class="mx-2">|</span>
                                <i class="fas fa-clock me-1"></i>
                                {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                        {% if not notification.is_read %}
                        <a href="{{ url_for('mark_notification_read', notification_id=notification.id, notification_type=notification.notification_type) }}"
                            class="btn btn-sm btn-outline-success" onclick="markAsReadAndUpdate(event, this)">
                            <i class="fas fa-check me-1"></i>
                            تحديد كمقروء
                        </a>
                        {% endif %}
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد إشعارات من المفتش</p>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- تبويب الإشعارات المرسلة -->
                {% if current_user_role in ['inspector', 'admin'] %}
                <div class="tab-pane fade {% if current_user_role == 'admin' %}show active{% endif %}"
                    id="sent-notifications" role="tabpanel">
                    {% if notifications_data.sent_notifications %}
                    {% for notification in notifications_data.sent_notifications %}
                    <div class="alert alert-info notification-item mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <h6 class="mb-0 fw-bold">{{ notification.title }}</h6>
                            <span class="badge bg-info ms-2">
                                {% if current_user_role == 'inspector' %}
                                مرسل للأساتذة
                                {% else %}
                                مرسل للمفتشين
                                {% endif %}
                            </span>
                            {% if not notification.is_read %}
                            <span class="badge bg-warning ms-1">غير مقروء</span>
                            {% else %}
                            <span class="badge bg-success ms-1">مقروء</span>
                            {% endif %}
                        </div>
                        <p class="mb-2">{{ notification.message }}</p>
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>
                            إلى: {{ notification.receiver.username }}
                            <span class="mx-2">|</span>
                            <i class="fas fa-clock me-1"></i>
                            {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </small>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد إشعارات مرسلة</p>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- تبويب الإشعارات العامة -->
                {% if notifications_data.general_notifications %}
                <div class="tab-pane fade {% if current_user_role == 'admin' and not notifications_data.sent_notifications %}show active{% endif %}"
                    id="general-notifications" role="tabpanel">
                    {% for notification in notifications_data.general_notifications %}
                    <div
                        class="alert {% if notification.is_read_by_user %}alert-light{% else %}alert-warning{% endif %} notification-item d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <h6 class="mb-0 {% if not notification.is_read_by_user %}fw-bold{% endif %}">
                                    {% if not notification.is_read_by_user %}
                                    <i class="fas fa-circle text-warning me-2" style="font-size: 0.5rem;"></i>
                                    {% endif %}
                                    {{ notification.title }}
                                </h6>
                                <span class="badge bg-warning ms-2">
                                    {% if notification.target_type == 'all' %}
                                    للجميع
                                    {% elif notification.target_type == 'role' %}
                                    {{ notification.target_role }}
                                    {% endif %}
                                </span>
                            </div>
                            <p class="mb-2">{{ notification.message }}</p>
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>
                                من: {{ notification.sender.username }}
                                <span class="mx-2">|</span>
                                <i class="fas fa-clock me-1"></i>
                                {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                        {% if not notification.is_read_by_user %}
                        <a href="{{ url_for('mark_notification_read', notification_id=notification.id, notification_type='general') }}"
                            class="btn btn-sm btn-outline-warning" onclick="markAsReadAndUpdate(event, this)">
                            <i class="fas fa-check me-1"></i>
                            تحديد كمقروء
                        </a>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // ملاحظة: الإشعارات لا تختفي تلقائياً - تبقى مرئية حتى يقرأها المستخدم أو يحدث الصفحة
    // دالة تحديد الإشعار كمقروء مع تحديث الواجهة
    function markAsReadAndUpdate(event, element) {
        event.preventDefault();

        const url = element.href;

        // إرسال طلب تحديد الإشعار كمقروء
        fetch(url, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => {
                if (response.ok) {
                    // تحديث مظهر الإشعار
                    const alertDiv = element.closest('.alert');
                    if (alertDiv) {
                        alertDiv.classList.remove('alert-primary', 'alert-success', 'alert-warning');
                        alertDiv.classList.add('alert-light');

                        // إزالة النقطة المضيئة
                        const circle = alertDiv.querySelector('.fas.fa-circle');
                        if (circle) {
                            circle.remove();
                        }

                        // إزالة الخط العريض
                        const title = alertDiv.querySelector('h6');
                        if (title) {
                            title.classList.remove('fw-bold');
                        }

                        // إخفاء الزر
                        element.style.display = 'none';
                    }

                    // تحديث عداد الإشعارات في القائمة الجانبية
                    if (typeof updateNotificationBadge === 'function') {
                        updateNotificationBadge();
                    }

                    // إظهار رسالة نجاح
                    showSuccessMessage('تم تحديد الإشعار كمقروء');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء تحديد الإشعار كمقروء');
            });
    }

    // دالة إظهار رسالة النجاح
    function showSuccessMessage(message) {
        const successDiv = document.getElementById('success-message');
        const successText = document.getElementById('success-text');

        if (successDiv && successText) {
            successText.textContent = message;
            successDiv.style.display = 'block';
            successDiv.classList.add('show');

            // إخفاء الرسالة بعد 5 ثوان (يمكن للمستخدم إغلاقها يدوياً)
            setTimeout(() => {
                hideSuccessMessage();
            }, 5000);
        }
    }

    // دالة إخفاء رسالة النجاح
    function hideSuccessMessage() {
        const successDiv = document.getElementById('success-message');
        if (successDiv) {
            successDiv.classList.remove('show');
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 150);
        }
    }
</script>
{% endblock %}