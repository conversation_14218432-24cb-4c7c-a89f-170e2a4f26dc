{"name": "Jinja H<PERSON>", "scopeName": "text.html.jinja", "fileTypes": ["html", "htm", "j2", "jinja", "jinja2"], "patterns": [{"include": "#jinja-comment"}, {"include": "#jinja-statement"}, {"include": "#jinja-expression"}, {"include": "text.html.basic"}], "repository": {"jinja-comment": {"name": "comment.block.jinja", "begin": "\\{#", "end": "#\\}", "patterns": [{"name": "comment.block.jinja", "match": ".*"}]}, "jinja-statement": {"name": "meta.tag.template.jinja", "begin": "\\{%", "end": "%\\}", "patterns": [{"name": "keyword.control.jinja", "match": "\\b(if|else|elif|endif|for|endfor|while|endwhile|block|endblock|extends|include|import|from|set|macro|endmacro|call|endcall|filter|endfilter|with|endwith|autoescape|endautoescape|trans|endtrans|pluralize|raw|endraw)\\b"}, {"name": "variable.other.jinja", "match": "\\b[a-zA-Z_][a-zA-Z0-9_]*\\b"}]}, "jinja-expression": {"name": "meta.tag.template.value.jinja", "begin": "\\{\\{", "end": "\\}\\}", "patterns": [{"name": "variable.other.jinja", "match": "\\b[a-zA-Z_][a-zA-Z0-9_]*\\b"}, {"name": "keyword.operator.jinja", "match": "\\|"}]}}}