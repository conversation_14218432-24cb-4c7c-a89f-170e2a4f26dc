#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار عرض الكفاءات في جدول آخر تحديثات التقدم
"""

from app import app, db
from models_new import ProgressEntry, LevelDataEntry, User, Role
from datetime import date

def test_competency_display():
    """
    اختبار عرض الكفاءات للمستخدمين
    """
    with app.app_context():
        print("=== اختبار عرض الكفاءات ===\n")
        
        # اختبار المستخدم tahar
        tahar = User.query.filter_by(username='tahar').first()
        if tahar:
            print(f"المستخدم: {tahar.username} (ID: {tahar.id})")
            
            # الحصول على آخر 3 تقدمات
            recent_progress = ProgressEntry.query.filter_by(
                user_id=tahar.id
            ).order_by(ProgressEntry.date.desc()).limit(3).all()
            
            print(f"آخر {len(recent_progress)} تقدمات:")
            
            for i, entry in enumerate(recent_progress, 1):
                print(f"\n  {i}. سجل التقدم ID: {entry.id}")
                print(f"     التاريخ: {entry.date}")
                print(f"     الحالة: {entry.status}")
                print(f"     competency_id: {entry.competency_id}")
                print(f"     material_id: {entry.material_id}")
                
                # اختبار الكفاءة
                if entry.competency_id:
                    competency = LevelDataEntry.query.filter_by(
                        id=entry.competency_id, 
                        entry_type='competency'
                    ).first()
                    
                    if competency:
                        print(f"     الكفاءة: {competency.name}")
                        print(f"     وصف الكفاءة: {competency.description}")
                        
                        # اختبار المادة المعرفية
                        if competency.parent_id:
                            material = LevelDataEntry.query.filter_by(
                                id=competency.parent_id,
                                entry_type='material'
                            ).first()
                            if material:
                                print(f"     المادة المعرفية: {material.name}")
                    else:
                        print(f"     ⚠️ الكفاءة غير موجودة في LevelDataEntry")
                else:
                    print(f"     ⚠️ لا يوجد competency_id")
        
        print("\n" + "="*60)
        
        # اختبار المستخدم teacher
        teacher = User.query.filter_by(username='teacher').first()
        if teacher:
            print(f"المستخدم: {teacher.username} (ID: {teacher.id})")
            
            # الحصول على آخر 3 تقدمات
            recent_progress = ProgressEntry.query.filter_by(
                user_id=teacher.id
            ).order_by(ProgressEntry.date.desc()).limit(3).all()
            
            print(f"آخر {len(recent_progress)} تقدمات:")
            
            for i, entry in enumerate(recent_progress, 1):
                print(f"\n  {i}. سجل التقدم ID: {entry.id}")
                print(f"     التاريخ: {entry.date}")
                print(f"     الحالة: {entry.status}")
                print(f"     competency_id: {entry.competency_id}")
                print(f"     material_id: {entry.material_id}")
                
                # اختبار الكفاءة
                if entry.competency_id:
                    competency = LevelDataEntry.query.filter_by(
                        id=entry.competency_id, 
                        entry_type='competency'
                    ).first()
                    
                    if competency:
                        print(f"     الكفاءة: {competency.name}")
                        print(f"     وصف الكفاءة: {competency.description}")
                        
                        # اختبار المادة المعرفية
                        if competency.parent_id:
                            material = LevelDataEntry.query.filter_by(
                                id=competency.parent_id,
                                entry_type='material'
                            ).first()
                            if material:
                                print(f"     المادة المعرفية: {material.name}")
                    else:
                        print(f"     ⚠️ الكفاءة غير موجودة في LevelDataEntry")
                else:
                    print(f"     ⚠️ لا يوجد competency_id")

def test_add_sample_progress():
    """
    إضافة تقدم تجريبي للاختبار
    """
    with app.app_context():
        print("\n=== إضافة تقدم تجريبي ===\n")
        
        # البحث عن كفاءة عشوائية
        competency = LevelDataEntry.query.filter_by(entry_type='competency').first()
        
        if not competency:
            print("لا توجد كفاءات في النظام!")
            return
        
        print(f"الكفاءة المختارة: {competency.name} (ID: {competency.id})")
        
        # البحث عن المستخدم tahar
        tahar = User.query.filter_by(username='tahar').first()
        if not tahar:
            print("المستخدم tahar غير موجود!")
            return
        
        # إنشاء تقدم جديد
        new_progress = ProgressEntry(
            user_id=tahar.id,
            competency_id=competency.id,
            date=date.today(),
            status='completed',
            notes='اختبار عرض الكفاءة'
        )
        
        try:
            db.session.add(new_progress)
            db.session.commit()
            print(f"تم إضافة تقدم جديد بنجاح (ID: {new_progress.id})")
        except Exception as e:
            db.session.rollback()
            print(f"خطأ في إضافة التقدم: {str(e)}")

if __name__ == "__main__":
    print("1. اختبار عرض الكفاءات الحالية...")
    test_competency_display()
    
    print("\n" + "="*60)
    print("2. إضافة تقدم تجريبي...")
    test_add_sample_progress()
    
    print("\n" + "="*60)
    print("3. اختبار عرض الكفاءات بعد الإضافة...")
    test_competency_display()
    
    print("\nتم الانتهاء من الاختبار!")
