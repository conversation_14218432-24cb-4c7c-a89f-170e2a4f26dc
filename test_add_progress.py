#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إضافة التقدم الجديد
"""

from app import app, db
from models_new import ProgressEntry, LevelDataEntry, User, Role, EducationalLevel
from datetime import date

def test_competency_hierarchy():
    """
    اختبار التسلسل الهرمي للكفاءات
    """
    with app.app_context():
        print("=== اختبار التسلسل الهرمي للكفاءات ===\n")
        
        # البحث عن كفاءة عشوائية
        competency = LevelDataEntry.query.filter_by(entry_type='competency').first()
        
        if not competency:
            print("لا توجد كفاءات في النظام!")
            return
        
        print(f"الكفاءة المختارة: {competency.name} (ID: {competency.id})")
        
        # تتبع التسلسل الهرمي
        material_id = None
        domain_id = None
        subject_id = None
        level_id = None
        
        # البحث عن المادة المعرفية (parent الكفاءة)
        if competency.parent_id:
            material = LevelDataEntry.query.filter_by(
                id=competency.parent_id,
                entry_type='material'
            ).first()
            
            if material:
                material_id = material.id
                print(f"المادة المعرفية: {material.name} (ID: {material_id})")
                
                # البحث عن الميدان (parent المادة المعرفية)
                if material.parent_id:
                    domain = LevelDataEntry.query.filter_by(
                        id=material.parent_id,
                        entry_type='domain'
                    ).first()
                    
                    if domain:
                        domain_id = domain.id
                        print(f"الميدان: {domain.name} (ID: {domain_id})")
                        
                        # البحث عن المادة الدراسية (parent الميدان)
                        if domain.parent_id:
                            subject = LevelDataEntry.query.filter_by(
                                id=domain.parent_id,
                                entry_type='subject'
                            ).first()
                            
                            if subject:
                                subject_id = subject.id
                                print(f"المادة الدراسية: {subject.name} (ID: {subject_id})")
                                
                                # البحث عن المستوى من قاعدة البيانات
                                from models_new import LevelDatabase
                                level_db = LevelDatabase.query.filter_by(
                                    id=subject.database_id,
                                    is_active=True
                                ).first()
                                
                                if level_db:
                                    level_id = level_db.level_id
                                    level = EducationalLevel.query.get(level_id)
                                    if level:
                                        print(f"المستوى: {level.name} (ID: {level_id})")
        
        print(f"\nملخص المعلومات المستنتجة:")
        print(f"  - المستوى: {level_id}")
        print(f"  - المادة الدراسية: {subject_id}")
        print(f"  - الميدان: {domain_id}")
        print(f"  - المادة المعرفية: {material_id}")
        print(f"  - الكفاءة: {competency.id}")

def test_recent_progress():
    """
    اختبار عرض آخر التقدمات
    """
    with app.app_context():
        print("\n=== اختبار آخر التقدمات ===\n")
        
        # البحث عن معلم
        teacher = User.query.filter_by(role=Role.TEACHER).first()
        if not teacher:
            print("لا يوجد معلمون في النظام!")
            return
        
        print(f"المعلم: {teacher.username} (ID: {teacher.id})")
        
        # الحصول على آخر 5 تقدمات
        recent_progress = ProgressEntry.query.filter_by(
            user_id=teacher.id
        ).order_by(ProgressEntry.date.desc()).limit(5).all()
        
        print(f"آخر {len(recent_progress)} تقدمات:")
        
        for i, entry in enumerate(recent_progress, 1):
            level_name = entry.level.name if entry.level else 'غير محدد'
            subject_name = entry.subject.name if entry.subject else 'غير محدد'
            domain_name = entry.domain.name if entry.domain else 'غير محدد'
            material_name = entry.material.name if entry.material else 'غير محدد'
            
            print(f"  {i}. التاريخ: {entry.date}")
            print(f"     المستوى: {level_name}")
            print(f"     المادة: {subject_name}")
            print(f"     الميدان: {domain_name}")
            print(f"     المادة المعرفية: {material_name}")
            print(f"     الحالة: {entry.status}")
            print(f"     معرفات: level_id={entry.level_id}, material_id={entry.material_id}")
            print()

if __name__ == "__main__":
    print("1. اختبار التسلسل الهرمي...")
    test_competency_hierarchy()
    
    print("\n" + "="*60)
    print("2. اختبار آخر التقدمات...")
    test_recent_progress()
    
    print("\nتم الانتهاء من الاختبار!")
