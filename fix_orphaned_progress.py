#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لإصلاح السجلات المعزولة في قاعدة البيانات
"""

from app import app, db
from models_new import ProgressEntry, EducationalLevel, LevelDatabase, LevelDataEntry, User, Role

def fix_orphaned_progress():
    """
    إصلاح السجلات المعزولة وربطها بالمستويات المناسبة
    """
    with app.app_context():
        print("=== إصلاح السجلات المعزولة ===\n")
        
        # البحث عن المعلمين
        teachers = User.query.filter_by(role=Role.TEACHER).all()
        
        for teacher in teachers:
            print(f"معالجة المعلم: {teacher.username} (ID: {teacher.id})")
            
            # البحث عن السجلات المعزولة
            orphaned_entries = ProgressEntry.query.filter_by(
                user_id=teacher.id,
                level_id=None,
                material_id=None,
                domain_id=None,
                subject_id=None
            ).all()
            
            if not orphaned_entries:
                print(f"  لا توجد سجلات معزولة للمعلم {teacher.username}")
                continue
            
            print(f"  وجد {len(orphaned_entries)} سجل معزول")
            
            # الحصول على جميع المستويات النشطة
            levels = EducationalLevel.query.filter_by(is_active=True).all()
            
            # توزيع السجلات على المستويات
            entries_per_level = len(orphaned_entries) // len(levels)
            remainder = len(orphaned_entries) % len(levels)
            
            entry_index = 0
            updated_count = 0
            
            for level_idx, level in enumerate(levels):
                # تحديد عدد السجلات لهذا المستوى
                entries_for_this_level = entries_per_level
                if level_idx < remainder:
                    entries_for_this_level += 1
                
                if entries_for_this_level == 0:
                    continue
                
                print(f"    ربط {entries_for_this_level} سجل بالمستوى: {level.name}")
                
                # الحصول على قاعدة البيانات للمستوى
                level_db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
                if not level_db:
                    print(f"      تحذير: لا توجد قاعدة بيانات للمستوى {level.name}")
                    continue
                
                # الحصول على أول مادة معرفية في المستوى
                first_material = LevelDataEntry.query.filter_by(
                    database_id=level_db.id,
                    entry_type='material',
                    is_active=True
                ).first()
                
                if not first_material:
                    print(f"      تحذير: لا توجد مواد معرفية في المستوى {level.name}")
                    continue
                
                # ربط السجلات بهذا المستوى
                for i in range(entries_for_this_level):
                    if entry_index < len(orphaned_entries):
                        entry = orphaned_entries[entry_index]
                        
                        # تحديث معلومات السجل
                        entry.material_id = first_material.id
                        entry.level_id = level.id
                        
                        # ربط بالميدان والمادة الدراسية
                        if first_material.parent_id:
                            entry.domain_id = first_material.parent_id
                            domain = LevelDataEntry.query.get(first_material.parent_id)
                            if domain and domain.parent_id:
                                entry.subject_id = domain.parent_id
                        
                        print(f"      ربط السجل {entry.id} (تاريخ: {entry.date}, حالة: {entry.status})")
                        updated_count += 1
                        entry_index += 1
            
            # حفظ التغييرات
            try:
                db.session.commit()
                print(f"  تم تحديث {updated_count} سجل بنجاح للمعلم {teacher.username}")
            except Exception as e:
                db.session.rollback()
                print(f"  خطأ في حفظ التغييرات للمعلم {teacher.username}: {str(e)}")
            
            print()

def verify_fix():
    """
    التحقق من نجاح الإصلاح
    """
    with app.app_context():
        print("=== التحقق من نجاح الإصلاح ===\n")
        
        teachers = User.query.filter_by(role=Role.TEACHER).all()
        
        for teacher in teachers:
            # فحص السجلات المعزولة المتبقية
            remaining_orphaned = ProgressEntry.query.filter_by(
                user_id=teacher.id,
                level_id=None,
                material_id=None,
                domain_id=None,
                subject_id=None
            ).count()
            
            # فحص إجمالي السجلات المكتملة
            total_completed = ProgressEntry.query.filter_by(
                user_id=teacher.id,
                status='completed'
            ).count()
            
            # فحص السجلات المكتملة مع material_id
            completed_with_material = ProgressEntry.query.filter_by(
                user_id=teacher.id,
                status='completed'
            ).filter(ProgressEntry.material_id.isnot(None)).count()
            
            print(f"المعلم {teacher.username}:")
            print(f"  السجلات المعزولة المتبقية: {remaining_orphaned}")
            print(f"  إجمالي السجلات المكتملة: {total_completed}")
            print(f"  السجلات المكتملة مع material_id: {completed_with_material}")
            print()

if __name__ == "__main__":
    print("1. إصلاح السجلات المعزولة...")
    fix_orphaned_progress()
    
    print("\n2. التحقق من نجاح الإصلاح...")
    verify_fix()
    
    print("تم الانتهاء من الإصلاح!")
