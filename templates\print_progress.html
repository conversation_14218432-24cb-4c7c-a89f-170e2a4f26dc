<!DOCTYPE html>
<html lang="ar" dir="rtl">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>طباعة التقدمات - {{ selected_date }}</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <style>
            @media print {
                .no-print {
                    display: none !important;
                }

                body {
                    font-size: 11px;
                }

                .table {
                    font-size: 10px;
                }

                .table th,
                .table td {
                    padding: 4px !important;
                    line-height: 1.2 !important;
                }

                .page-break {
                    page-break-before: always;
                }

                @page {
                    margin: 0.8cm;
                    size: A4 landscape;
                }

                .print-container {
                    max-width: none !important;
                    width: 100% !important;
                    margin: 0 !important;
                    padding: 15px !important;
                }

                .header-section h1 {
                    font-size: 18px !important;
                    margin-bottom: 10px !important;
                }

                .header-section h3 {
                    font-size: 14px !important;
                }

                .info-section {
                    margin-bottom: 15px !important;
                    padding: 10px !important;
                }

                .competency-cell {
                    max-width: 200px !important;
                    word-wrap: break-word !important;
                    white-space: normal !important;
                    font-size: 9px !important;
                }

                .table-responsive {
                    overflow: visible !important;
                }

                .badge {
                    font-size: 8px !important;
                    padding: 2px 4px !important;
                }

                .summary-compact {
                    padding: 8px !important;
                    margin-bottom: 10px !important;
                }

                .summary-compact h6 {
                    font-size: 10px !important;
                    margin-bottom: 5px !important;
                }

                .summary-item {
                    margin: 0 5px !important;
                }

                .summary-item .badge {
                    font-size: 9px !important;
                }

                .summary-item small {
                    font-size: 8px !important;
                }
            }

            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background-color: #f8f9fa;
            }

            .print-container {
                background: white;
                padding: 30px;
                margin: 20px auto;
                max-width: 1000px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            }

            .header-section {
                text-align: center;
                border-bottom: 3px solid #007bff;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }

            .info-section {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
            }

            .table th {
                background-color: #007bff;
                color: white;
                font-weight: bold;
                text-align: center;
                vertical-align: middle;
            }

            .table td {
                vertical-align: middle;
                text-align: center;
            }

            .status-completed {
                background-color: #d4edda;
                color: #155724;
            }

            .status-in-progress {
                background-color: #fff3cd;
                color: #856404;
            }

            .status-planned {
                background-color: #f8d7da;
                color: #721c24;
            }

            .footer-section {
                margin-top: 40px;
                text-align: center;
                color: #6c757d;
                font-size: 0.9em;
            }

            /* تحسين عرض الكفاءات في الشاشة العادية */
            .competency-cell {
                word-wrap: break-word;
                white-space: normal;
                max-width: 300px;
            }

            .table-responsive {
                overflow-x: auto;
            }

            /* تصميم مضغوط للملخص */
            .summary-compact {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                text-align: center;
            }

            .summary-item {
                display: inline-flex;
                align-items: center;
                margin: 0 10px;
            }

            .gap-4 {
                gap: 1.5rem !important;
            }

            /* إظهار الأزرار في الشاشة العادية فقط */
            @media screen {
                .no-print {
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                }
            }
        </style>
    </head>

    <body>
        <div class="print-container">
            <!-- Header Section -->
            <div class="header-section">
                <h1 class="mb-3">
                    <i class="fas fa-graduation-cap text-primary"></i>
                    تقرير التقدمات اليومية
                </h1>
                <h3 class="text-muted">نظام تقفي للمتابعة التربوية</h3>
            </div>

            <!-- Info Section -->
            <div class="info-section">
                <div class="row">
                    <div class="col-md-4">
                        <strong>
                            <i class="fas fa-user text-primary"></i>
                            اسم الأستاذ:
                        </strong>
                        <span>{{ teacher_name }}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>
                            <i class="fas fa-calendar text-primary"></i>
                            التاريخ:
                        </strong>
                        <span>{{ selected_date }}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>
                            <i class="fas fa-list-ol text-primary"></i>
                            عدد التقدمات:
                        </strong>
                        <span>{{ total_entries }}</span>
                    </div>
                </div>
            </div>

            <!-- Print Button -->
            <div class="text-center mb-4 no-print">
                <button onclick="window.print()" class="btn btn-primary btn-lg">
                    <i class="fas fa-print me-2"></i>
                    طباعة التقرير
                </button>
                <a href="{{ url_for('teacher_dashboard') }}" class="btn btn-secondary btn-lg ms-3">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للوحة التحكم
                </a>
            </div>

            <!-- Progress Table -->
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th style="width: 5%;">#</th>
                            <th style="width: 10%;">المستوى</th>
                            <th style="width: 10%;">المادة الدراسية</th>
                            <th style="width: 12%;">الميدان</th>
                            <th style="width: 13%;">المادة المعرفية</th>
                            <th style="width: 35%;">الكفاءة المستهدفة</th>
                            <th style="width: 8%;">الحالة</th>
                            <th style="width: 7%;">ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in progress_data %}
                        <tr
                            class="{% if item.entry.status == 'completed' %}status-completed{% elif item.entry.status == 'in_progress' %}status-in-progress{% elif item.entry.status == 'planned' %}status-planned{% endif %}">
                            <td><strong>{{ loop.index }}</strong></td>
                            <td>{{ item.entry.level.name if item.entry.level else 'غير محدد' }}</td>
                            <td>{{ item.entry.subject.name if item.entry.subject else 'غير محدد' }}</td>
                            <td>{{ item.entry.domain.name if item.entry.domain else 'غير محدد' }}</td>
                            <td>{{ item.entry.material.name if item.entry.material else 'غير محدد' }}</td>
                            <td class="competency-cell" style="text-align: right; padding: 8px;">
                                <small>{{ item.competency_name }}</small>
                            </td>
                            <td>
                                <span class="badge 
                                {% if item.entry.status == 'completed' %}bg-success
                                {% elif item.entry.status == 'in_progress' %}bg-warning
                                {% else %}bg-danger{% endif %}">
                                    {{ item.status_text }}
                                </span>
                            </td>
                            <td style="font-size: 0.8em;">
                                {{ item.entry.notes[:50] + '...' if item.entry.notes and item.entry.notes|length > 50
                                else (item.entry.notes or '') }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Summary Section - Compact Horizontal Layout -->
            <div class="row mt-3">
                <div class="col-12">
                    <div class="summary-compact">
                        <h6 class="mb-2">
                            <i class="fas fa-chart-pie text-primary"></i>
                            ملخص التقدمات:
                        </h6>
                        <div class="d-flex justify-content-center gap-4">
                            {% set completed_count = progress_data|selectattr('entry.status', 'eq',
                            'completed')|list|length %}
                            {% set in_progress_count = progress_data|selectattr('entry.status', 'eq',
                            'in_progress')|list|length %}
                            {% set planned_count = progress_data|selectattr('entry.status', 'eq', 'planned')|list|length
                            %}

                            <div class="summary-item">
                                <span class="badge bg-success fs-6">{{ completed_count }}</span>
                                <small class="text-success ms-1">مكتملة</small>
                            </div>
                            <div class="summary-item">
                                <span class="badge bg-warning fs-6">{{ in_progress_count }}</span>
                                <small class="text-warning ms-1">قيد التنفيذ</small>
                            </div>
                            <div class="summary-item">
                                <span class="badge bg-danger fs-6">{{ planned_count }}</span>
                                <small class="text-danger ms-1">مخططة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer Section -->
            <div class="footer-section">
                <hr>
                <p class="mb-1">
                    <i class="fas fa-calendar-alt"></i>
                    تم إنشاء هذا التقرير في: <span id="current-datetime"></span>
                </p>
                <p class="mb-0">
                    <i class="fas fa-laptop"></i>
                    نظام تقفي للمتابعة التربوية - جميع الحقوق محفوظة
                </p>
            </div>
        </div>

        <script>
            // تلقائياً فتح نافذة الطباعة عند تحميل الصفحة
            window.addEventListener('load', function () {
                // إظهار التاريخ والوقت الحالي
                const now = new Date();
                const dateTimeString = now.getFullYear() + '-' +
                    String(now.getMonth() + 1).padStart(2, '0') + '-' +
                    String(now.getDate()).padStart(2, '0') + ' ' +
                    String(now.getHours()).padStart(2, '0') + ':' +
                    String(now.getMinutes()).padStart(2, '0') + ':' +
                    String(now.getSeconds()).padStart(2, '0');

                const datetimeElement = document.getElementById('current-datetime');
                if (datetimeElement) {
                    datetimeElement.textContent = dateTimeString;
                }


            });
        </script>
    </body>

</html>