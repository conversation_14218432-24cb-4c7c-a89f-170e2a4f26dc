{% extends "base.html" %}

{% block title %}الإدارة المتقدمة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-users-cog me-2"></i>الإدارة المتقدمة
        </h1>
        <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للوحة الأدمن
        </a>
    </div>

    <!-- تحذير عام -->
    <div class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>تحذير:</strong> هذه الصفحة تحتوي على أدوات إدارة متقدمة وحساسة. يرجى استخدامها بحذر شديد.
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>إجمالي سجلات التقدم</div>
                        <div class="h3">{{ total_progress_entries }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>إجمالي الأساتذة</div>
                        <div class="h3">{{ total_teachers }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>أساتذة لديهم تقدمات</div>
                        <div class="h3">{{ teachers_with_progress }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>أساتذة بدون تقدمات</div>
                        <div class="h3">{{ total_teachers - teachers_with_progress }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات الإدارة المتقدمة -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tools me-2"></i>أدوات الإدارة المتقدمة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- تصفير عدّاد التقدم -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-trash-alt me-2"></i>تصفير عدّاد التقدم
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        <strong>الوصف:</strong> حذف جميع سجلات التقدم لكافة الأساتذة وإعادة تصفير النسب الإجمالية والتفصيلية.
                                    </p>
                                    <p class="card-text">
                                        <strong>الاستخدام:</strong> في نهاية الموسم أو السنة الدراسية لبدء موسم جديد.
                                    </p>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                                    </div>
                                    <button type="button" class="btn btn-danger btn-lg w-100" 
                                            data-bs-toggle="modal" data-bs-target="#resetProgressModal">
                                        <i class="fas fa-trash-alt me-2"></i>تصفير عدّاد التقدم
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- مساحة لأدوات أخرى مستقبلية -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-plus me-2"></i>أدوات إضافية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text text-muted">
                                        سيتم إضافة أدوات إدارة متقدمة أخرى هنا في المستقبل.
                                    </p>
                                    <button type="button" class="btn btn-secondary w-100" disabled>
                                        <i class="fas fa-cog me-2"></i>قريباً...
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد تصفير التقدم -->
<div class="modal fade" id="resetProgressModal" tabindex="-1" aria-labelledby="resetProgressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="resetProgressModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>تأكيد تصفير عدّاد التقدم
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>تحذير شديد!</h5>
                    <p class="mb-0">
                        أنت على وشك حذف <strong>جميع</strong> سجلات التقدم لكافة الأساتذة في النظام.
                        هذا الإجراء سيؤدي إلى:
                    </p>
                </div>
                
                <ul class="list-group list-group-flush mb-3">
                    <li class="list-group-item">
                        <i class="fas fa-times text-danger me-2"></i>
                        حذف جميع سجلات التقدم ({{ total_progress_entries }} سجل)
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-times text-danger me-2"></i>
                        تصفير نسب التقدم الإجمالية لجميع الأساتذة
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-times text-danger me-2"></i>
                        تصفير نسب التقدم التفصيلية لجميع المواد
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-times text-danger me-2"></i>
                        فقدان جميع البيانات التاريخية للتقدم
                    </li>
                </ul>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> لن يتم حذف بيانات المستخدمين أو المناهج الدراسية، فقط سجلات التقدم.
                </div>

                <form id="resetProgressForm" action="{{ url_for('reset_all_progress') }}" method="POST">
                    <div class="mb-3">
                        <label for="adminPassword" class="form-label">
                            <strong>أدخل كلمة مرور حسابك للتأكيد:</strong>
                        </label>
                        <input type="password" class="form-control" id="adminPassword" name="admin_password" 
                               placeholder="كلمة مرور الأدمن" required>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmReset" name="confirm_reset" required>
                        <label class="form-check-label" for="confirmReset">
                            <strong>أؤكد أنني أفهم عواقب هذا الإجراء وأريد المتابعة</strong>
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>إلغاء
                </button>
                <button type="submit" form="resetProgressForm" class="btn btn-danger">
                    <i class="fas fa-trash-alt me-1"></i>تأكيد التصفير
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// تأكيد إضافي قبل الإرسال
document.getElementById('resetProgressForm').addEventListener('submit', function(e) {
    const password = document.getElementById('adminPassword').value;
    const confirmed = document.getElementById('confirmReset').checked;
    
    if (!password || !confirmed) {
        e.preventDefault();
        alert('يرجى إدخال كلمة المرور وتأكيد الموافقة');
        return;
    }
    
    const finalConfirm = confirm('هل أنت متأكد تماماً من رغبتك في تصفير جميع سجلات التقدم؟\n\nهذا الإجراء لا يمكن التراجع عنه!');
    if (!finalConfirm) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
