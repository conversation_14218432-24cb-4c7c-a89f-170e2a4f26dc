{% extends 'base.html' %}

{% block content %}
<!-- <PERSON><PERSON><PERSON><PERSON> principal -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2>
                <i class="fas fa-database me-2"></i>
                إدارة قواعد البيانات للمستويات التعليمية
            </h2>
        </div>
    </div>
</div>

<!-- Pestañas de navegación -->
<div class="row mb-4">
    <div class="col-md-12">
        <ul class="nav nav-tabs" id="databaseTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="databases-tab" data-bs-toggle="tab" data-bs-target="#databases" type="button" role="tab" aria-controls="databases" aria-selected="true">
                    <i class="fas fa-database me-1"></i> قواعد البيانات الحالية
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="create-tab" data-bs-toggle="tab" data-bs-target="#create" type="button" role="tab" aria-controls="create" aria-selected="false">
                    <i class="fas fa-plus-circle me-1"></i> إنشاء قاعدة بيانات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="import-export-tab" data-bs-toggle="tab" data-bs-target="#import-export" type="button" role="tab" aria-controls="import-export" aria-selected="false">
                    <i class="fas fa-exchange-alt me-1"></i> استيراد / تصدير
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="primary-levels-tab" data-bs-toggle="tab" data-bs-target="#primary-levels" type="button" role="tab" aria-controls="primary-levels" aria-selected="false">
                    <i class="fas fa-graduation-cap me-1"></i> إنشاء مستويات التعليم الابتدائي
                </button>
            </li>

        </ul>
    </div>
</div>

<!-- Contenido de las pestañas -->
<div class="tab-content" id="databaseTabsContent">
    <!-- Pestaña: Bases de datos actuales -->
    <div class="tab-pane fade show active" id="databases" role="tabpanel" aria-labelledby="databases-tab">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-1"></i>
                        قواعد البيانات الحالية
                    </h5>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>المستوى التعليمي</th>
                                <th>اسم قاعدة البيانات</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th class="text-center">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for db in databases %}
                            <tr>
                                <td>{{ db.level.name }}</td>
                                <td>{{ db.name }}</td>
                                <td>
                                    {% if db.is_active %}
                                    <span class="badge bg-success">مفعلة</span>
                                    {% else %}
                                    <span class="badge bg-danger">معطلة</span>
                                    {% endif %}
                                </td>
                                <td>{{ db.created_at.strftime('%Y-%m-%d') }}</td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('view_database', db_id=db.id) }}" class="btn btn-sm btn-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_database', db_id=db.id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('export_database_data', db_id=db.id) }}" class="btn btn-sm btn-info" title="تصدير إلى Excel">
                                            <i class="fas fa-file-export"></i>
                                        </a>
                                        {% if db.is_active %}
                                        <a href="{{ url_for('toggle_database', db_id=db.id, action='deactivate') }}" class="btn btn-sm btn-secondary" title="تعطيل">
                                            <i class="fas fa-ban"></i>
                                        </a>
                                        {% else %}
                                        <a href="{{ url_for('toggle_database', db_id=db.id, action='activate') }}" class="btn btn-sm btn-success" title="تفعيل">
                                            <i class="fas fa-check"></i>
                                        </a>
                                        {% endif %}
                                        <button class="btn btn-sm btn-danger delete-db" data-id="{{ db.id }}" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                            {% if not databases %}
                            <tr>
                                <td colspan="5" class="text-center">لا توجد قواعد بيانات حالية</td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Pestaña: Crear base de datos -->
    <div class="tab-pane fade" id="create" role="tabpanel" aria-labelledby="create-tab">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-plus-circle me-1"></i>
                            إضافة قاعدة بيانات جديدة
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="databaseForm" method="POST" action="{{ url_for('add_database') }}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="level_id" class="form-label">المستوى التعليمي</label>
                                    <select class="form-select" id="level_id" name="level_id" required>
                                        <option value="" selected disabled>اختر المستوى</option>
                                        {% for level in levels %}
                                        <option value="{{ level.id }}">{{ level.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">اسم قاعدة البيانات</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="file_path" class="form-label">مسار الملف</label>
                                <input type="text" class="form-control" id="file_path" name="file_path" placeholder="databases/example.db" required>
                                <div class="form-text text-muted">مثال: databases/level1.db</div>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">مفعلة</label>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i> إضافة قاعدة البيانات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pestaña: Importar/Exportar -->
    <div class="tab-pane fade" id="import-export" role="tabpanel" aria-labelledby="import-export-tab">
        <div class="row">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-export me-1"></i> تصدير البيانات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h6>تصدير جميع قواعد البيانات</h6>
                            <p class="text-muted">تصدير جميع قواعد البيانات إلى ملف Excel واحد يحتوي على جميع المستويات والمواد والميادين والمواد المعرفية والكفاءات.</p>
                            <a href="{{ url_for('export_all_databases') }}" class="btn btn-success">
                                <i class="fas fa-file-export me-1"></i> تصدير جميع قواعد البيانات
                            </a>
                        </div>

                        <div class="mb-4">
                            <h6>تصدير المستويات فقط</h6>
                            <p class="text-muted">تصدير المستويات التعليمية فقط إلى ملف Excel.</p>
                            <a href="{{ url_for('export_data', model_name='levels') }}" class="btn btn-info">
                                <i class="fas fa-file-export me-1"></i> تصدير المستويات
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-import me-1"></i> استيراد البيانات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h6>استيراد جميع المستويات</h6>
                            <p class="text-muted">استيراد جميع المستويات والمواد الدراسية والميادين والمواد المعرفية والكفاءات من ملف Excel واحد.</p>
                            <a href="{{ url_for('import_all_levels_page') }}" class="btn btn-danger">
                                <i class="fas fa-file-import me-1"></i> استيراد جميع المستويات
                            </a>
                        </div>

                        <div class="mb-4">
                            <h6>استيراد بيانات لقاعدة بيانات محددة</h6>
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-1"></i> ملاحظة مهمة</h6>
                                <p class="mb-0">
                                    <strong>الوضع الافتراضي:</strong> سيتم حذف جميع البيانات الحالية في قاعدة البيانات وإعادة كتابتها بالكامل حسب الملف المستورد لتجنب التكرار.
                                    <br><strong>هذا يضمن:</strong> عدم وجود مواد أو ميادين مكررة في قاعدة البيانات.
                                </p>
                            </div>
                            <form id="importForm" method="POST" action="#" enctype="multipart/form-data" onsubmit="submitImportForm(event)">
                                <div class="mb-3">
                                    <label for="import_db_id" class="form-label">قاعدة البيانات</label>
                                    <select class="form-select" id="import_db_id" name="db_id" required>
                                        <option value="" selected disabled>اختر قاعدة البيانات</option>
                                        {% for db in databases %}
                                        {% if db.is_active %}
                                        <option value="{{ db.id }}">{{ db.level.name }} - {{ db.name }}</option>
                                        {% endif %}
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="import_file" class="form-label">ملف البيانات (Excel)</label>
                                    <input type="file" class="form-control" id="import_file" name="file" accept=".xlsx" required>
                                    <div class="form-text">يجب أن يحتوي الملف على أوراق بأسماء: Subjects, Domains, Materials, Competencies</div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="keep_existing" name="keep_existing">
                                        <label class="form-check-label" for="keep_existing">
                                            <strong>الاحتفاظ بالبيانات الحالية</strong> (قد يؤدي إلى تكرار البيانات)
                                        </label>
                                        <div class="form-text text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            تحديد هذا الخيار قد يؤدي إلى وجود مواد أو ميادين مكررة
                                        </div>
                                    </div>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-file-import me-1"></i> استيراد البيانات (حذف وإعادة كتابة)
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تبويب: إنشاء مستويات التعليم الابتدائي -->
    <div class="tab-pane fade" id="primary-levels" role="tabpanel" aria-labelledby="primary-levels-tab">
        <div class="row">
            <div class="col-md-10 mx-auto">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-graduation-cap me-1"></i> إنشاء مستويات التعليم الابتدائي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-1"></i> معلومات</h6>
                            <p class="mb-0">
                                هذه الميزة تقوم بإنشاء المستويات الخمسة للتعليم الابتدائي مع قواعد البيانات الخاصة بكل مستوى،
                                وتضيف المواد الدراسية والميادين الثابتة لكل مستوى.
                            </p>
                        </div>

                        <div class="mb-4">
                            <h6>المستويات التي سيتم إنشاؤها:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-group mb-3">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span><i class="fas fa-child me-2"></i>السنة الأولى ابتدائي</span>
                                            <span class="badge bg-primary rounded-pill">13 مادة</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span><i class="fas fa-child me-2"></i>السنة الثانية ابتدائي</span>
                                            <span class="badge bg-primary rounded-pill">13 مادة</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span><i class="fas fa-child me-2"></i>السنة الثالثة ابتدائي</span>
                                            <span class="badge bg-primary rounded-pill">13 مادة</span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-group mb-3">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span><i class="fas fa-child me-2"></i>السنة الرابعة ابتدائي</span>
                                            <span class="badge bg-primary rounded-pill">13 مادة</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span><i class="fas fa-child me-2"></i>السنة الخامسة ابتدائي</span>
                                            <span class="badge bg-primary rounded-pill">13 مادة</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h6>المواد الدراسية المشتركة:</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-book text-primary me-2"></i>اللغة العربية (20 ميدان)</li>
                                        <li><i class="fas fa-calculator text-success me-2"></i>الرياضيات (7 ميادين)</li>
                                        <li><i class="fas fa-mosque text-info me-2"></i>التربية الإسلامية (6 ميادين)</li>
                                        <li><i class="fas fa-flag text-warning me-2"></i>الفرنسية (1 ميدان)</li>
                                    </ul>
                                </div>
                                <div class="col-md-4">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-flask text-danger me-2"></i>التربية العلمية (5 ميادين)</li>
                                        <li><i class="fas fa-users text-secondary me-2"></i>التربية المدنية (4 ميادين)</li>
                                        <li><i class="fas fa-palette text-purple me-2"></i>التربية الفنية (6 ميادين)</li>
                                        <li><i class="fas fa-globe text-primary me-2"></i>التاريخ (4 ميادين)</li>
                                    </ul>
                                </div>
                                <div class="col-md-4">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-map text-success me-2"></i>الجغرافيا (4 ميادين)</li>
                                        <li><i class="fas fa-running text-info me-2"></i>التربية البدنية (4 ميادين)</li>
                                        <li><i class="fas fa-quran-book text-warning me-2"></i>حفظ القرآن (2 ميدان)</li>
                                        <li><i class="fas fa-language text-danger me-2"></i>الأمازيغية + الإنجليزية</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-1"></i> تحذير</h6>
                            <p class="mb-0">
                                إذا كانت المستويات موجودة مسبقاً، سيتم حذف جميع البيانات الموجودة في قواعد البيانات الخاصة بها
                                وإعادة إنشائها بالمواد والميادين الثابتة. المواد المعرفية والكفاءات يجب إضافتها يدوياً بعد ذلك.
                            </p>
                        </div>

                        <div class="d-grid">
                            <form method="POST" action="{{ url_for('create_primary_levels') }}" onsubmit="return confirm('هل أنت متأكد من إنشاء مستويات التعليم الابتدائي؟ سيتم حذف جميع البيانات الموجودة في قواعد البيانات الحالية.')">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-graduation-cap me-1"></i> إنشاء مستويات التعليم الابتدائي (5 مستويات)
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Modal de confirmación de eliminación -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف قاعدة البيانات هذه؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="confirmDelete" class="btn btn-danger">حذف</a>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Manejar la eliminación de bases de datos
        const deleteButtons = document.querySelectorAll('.delete-db');
        const confirmDeleteButton = document.getElementById('confirmDelete');

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const dbId = this.getAttribute('data-id');
                confirmDeleteButton.href = "{{ url_for('delete_database', db_id=0) }}".replace('0', dbId);

                const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
                deleteModal.show();
            });
        });
    });

    // Función para manejar el envío del formulario de importación
    function submitImportForm(event) {
        event.preventDefault();
        const form = event.target;
        const dbId = document.getElementById('import_db_id').value;

        if (!dbId) {
            alert('الرجاء اختيار قاعدة بيانات');
            return false;
        }

        form.action = "{{ url_for('import_database_data', db_id=0) }}".replace('0', dbId);
        form.submit();
    }
</script>
{% endblock %}
{% endblock %}
