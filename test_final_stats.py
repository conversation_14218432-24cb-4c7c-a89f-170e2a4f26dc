#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الإحصائيات النهائية بعد الإصلاح
"""

from app import app, db, calculate_level_progress_by_materials
from models_new import ProgressEntry, EducationalLevel, User, Role

def test_final_statistics():
    """
    اختبار الإحصائيات النهائية
    """
    with app.app_context():
        print("=== اختبار الإحصائيات النهائية ===\n")
        
        # البحث عن المعلم الأول
        teacher = User.query.filter_by(role=Role.TEACHER).first()
        if not teacher:
            print("لا يوجد معلمون في النظام!")
            return
        
        print(f"اختبار إحصائيات المعلم: {teacher.username} (ID: {teacher.id})\n")
        
        # فحص إجمالي السجلات المكتملة
        total_completed = ProgressEntry.query.filter_by(
            user_id=teacher.id,
            status='completed'
        ).count()
        print(f"إجمالي السجلات المكتملة: {total_completed}")
        
        # فحص الإحصائيات لكل مستوى
        levels = EducationalLevel.query.filter_by(is_active=True).all()
        total_calculated = 0
        
        print("\n=== إحصائيات المستويات ===")
        for level in levels:
            progress_stats = calculate_level_progress_by_materials(teacher.id, level.id)
            completed = progress_stats['completed_materials']
            total = progress_stats['total_materials']
            rate = progress_stats['completion_rate']
            
            print(f"{level.name}:")
            print(f"  المواد المكتملة: {completed}")
            print(f"  إجمالي المواد: {total}")
            print(f"  نسبة الإنجاز: {rate:.1f}%")
            
            total_calculated += completed
        
        print(f"\nمجموع المواد المكتملة المحسوبة: {total_calculated}")
        print(f"إجمالي السجلات المكتملة في قاعدة البيانات: {total_completed}")
        
        if total_calculated == total_completed:
            print("✅ الإحصائيات متطابقة! تم حل المشكلة بنجاح.")
        else:
            print("❌ لا تزال هناك مشكلة في الإحصائيات.")
            print(f"الفرق: {abs(total_calculated - total_completed)}")

if __name__ == "__main__":
    test_final_statistics()
