{"files.associations": {"*.html": "html", "**/templates/*.html": "html"}, "emmet.includeLanguages": {"html": "html"}, "javascript.validate.enable": false, "typescript.validate.enable": false, "html.validate.scripts": false, "html.validate.styles": false, "[html]": {"editor.defaultFormatter": "vscode.html-language-features", "editor.formatOnSave": true, "editor.quickSuggestions": {"other": true, "comments": false, "strings": true}}, "html.format.indentInnerHtml": true, "html.format.wrapLineLength": 120, "html.suggest.html5": true, "editor.tokenColorCustomizations": {"textMateRules": [{"scope": ["keyword.control.template.jinja", "keyword.control.jinja"], "settings": {"foreground": "#C586C0", "fontStyle": "bold"}}, {"scope": ["variable.other.template.jinja", "variable.other.jinja"], "settings": {"foreground": "#9CDCFE"}}, {"scope": ["string.quoted.double.template.jinja", "string.quoted.single.template.jinja"], "settings": {"foreground": "#CE9178"}}, {"scope": ["punctuation.definition.template.jinja", "punctuation.section.template.jinja"], "settings": {"foreground": "#DCDCAA"}}]}, "editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": true}