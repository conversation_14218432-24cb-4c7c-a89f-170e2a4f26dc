"""
تطبيق Ta9affi الجديد
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, make_response, send_file
from flask_login import LoginManager, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
import os
import pandas as pd
import re
from datetime import datetime, timezone
from functools import lru_cache
from io import BytesIO
import tempfile
try:
    import openpyxl
    from openpyxl.styles import Alignment, Font, PatternFill
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

from models_new import db, User, Role, RoleSettings, ALGERIAN_WILAYAS, EducationalLevel, Subject, Domain, KnowledgeMaterial, Competency, Schedule, ProgressEntry, LevelDatabase, LevelDataEntry, AdminInspectorNotification, InspectorTeacherNotification, GeneralNotification, GeneralNotificationRead, inspector_teacher

# تهيئة تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ta9affi_new.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['NOTIFICATIONS_PER_PAGE'] = 10  # عدد الإشعارات في الصفحة الواحدة

# تهيئة قاعدة البيانات
db.init_app(app)

# تهيئة مدير تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# إضافة فلتر nl2br لتنسيق النص في الرسائل
@app.template_filter('nl2br')
def nl2br(value):
    if value:
        return value.replace('\n', '<br>')
    return ''

# تحميل المستخدم
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# وظائف التحقق من صحة البيانات
def validate_algerian_phone(phone):
    """التحقق من صحة رقم الهاتف الجزائري"""
    if not phone:
        return False

    # إزالة المسافات والشرطات
    phone = re.sub(r'[\s\-]', '', phone)

    # أنماط أرقام الهاتف الجزائرية المقبولة
    patterns = [
        r'^0[5-7]\d{8}$',  # أرقام الجوال: 05, 06, 07 + 8 أرقام
        r'^0[2-4]\d{7}$',  # أرقام الهاتف الثابت: 02, 03, 04 + 7 أرقام
        r'^\+213[5-7]\d{8}$',  # أرقام دولية للجوال
        r'^\+213[2-4]\d{7}$'   # أرقام دولية للثابت
    ]

    return any(re.match(pattern, phone) for pattern in patterns)

def get_enabled_roles():
    """الحصول على الأدوار المتاحة للتسجيل"""
    enabled_roles = []

    # التحقق من إعدادات الأدوار في قاعدة البيانات
    role_settings = RoleSettings.query.filter_by(is_enabled=True).all()

    if role_settings:
        # إذا كانت هناك إعدادات، استخدمها
        for setting in role_settings:
            enabled_roles.append((setting.role_name, setting.display_name))
    else:
        # إذا لم تكن هناك إعدادات، استخدم الأدوار الافتراضية
        enabled_roles = [
            (Role.TEACHER, 'أستاذ'),
            (Role.INSPECTOR, 'مفتش')
        ]

    return enabled_roles

# الصفحة الرئيسية
@app.route('/')
def index():
    return render_template('index.html')

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password, password):
            # التحقق من حالة الحساب
            if not user.is_active:
                flash('هذا الحساب معطل. يرجى التواصل مع الإدارة.', 'danger')
                return render_template('login.html')

            login_user(user)
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')

    return render_template('login.html')

# تسجيل حساب جديد
@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        role = request.form.get('role')
        phone_number = request.form.get('phone_number')
        wilaya_code = request.form.get('wilaya_code')

        # التحقق من الحقول الإجبارية
        if not username or not email or not password or not role or not phone_number:
            flash('جميع الحقول مطلوبة عدا الولاية', 'danger')
            return render_template('register.html', enabled_roles=get_enabled_roles(), wilayas=ALGERIAN_WILAYAS)

        # التحقق من صحة رقم الهاتف
        if not validate_algerian_phone(phone_number):
            flash('رقم الهاتف غير صحيح. يرجى إدخال رقم هاتف جزائري صحيح', 'danger')
            return render_template('register.html', enabled_roles=get_enabled_roles(), wilayas=ALGERIAN_WILAYAS)

        # منع إنشاء حسابات إدارة من صفحة التسجيل العامة
        if role == Role.ADMIN:
            flash('لا يمكن إنشاء حسابات إدارة من هذه الصفحة', 'danger')
            return render_template('register.html', enabled_roles=get_enabled_roles(), wilayas=ALGERIAN_WILAYAS)

        # التحقق من أن الدور متاح للتسجيل
        enabled_roles = get_enabled_roles()
        if not any(role == enabled_role[0] for enabled_role in enabled_roles):
            flash('هذا الدور غير متاح للتسجيل حالياً', 'danger')
            return render_template('register.html', enabled_roles=enabled_roles, wilayas=ALGERIAN_WILAYAS)

        # التحقق من وجود اسم المستخدم أو البريد الإلكتروني
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل', 'danger')
            return render_template('register.html', enabled_roles=enabled_roles, wilayas=ALGERIAN_WILAYAS)

        if User.query.filter_by(email=email).first():
            flash('البريد الإلكتروني موجود بالفعل', 'danger')
            return render_template('register.html', enabled_roles=enabled_roles, wilayas=ALGERIAN_WILAYAS)

        # إنشاء مستخدم جديد (معطل بشكل افتراضي)
        hashed_password = generate_password_hash(password)
        new_user = User(
            username=username,
            email=email,
            password=hashed_password,
            role=role,
            phone_number=phone_number,
            wilaya_code=wilaya_code if wilaya_code else None,
            _is_active=False  # الحساب معطل حتى يتم تفعيله من قبل الإدارة
        )

        try:
            db.session.add(new_user)
            db.session.commit()
            flash('تم إنشاء الحساب بنجاح! سيتم تفعيله من قبل الإدارة قريباً.', 'info')
            return redirect(url_for('login'))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إنشاء الحساب', 'danger')
            return render_template('register.html', enabled_roles=get_enabled_roles(), wilayas=ALGERIAN_WILAYAS)

    return render_template('register.html', enabled_roles=get_enabled_roles(), wilayas=ALGERIAN_WILAYAS)

# لوحة التحكم
@app.route('/dashboard')
@login_required
def dashboard():
    if current_user.role == Role.ADMIN:
        return redirect(url_for('admin_dashboard'))
    elif current_user.role == Role.USER_MANAGER:
        return redirect(url_for('user_manager_dashboard'))
    elif current_user.role == Role.INSPECTOR:
        return redirect(url_for('inspector_dashboard'))
    else:
        return redirect(url_for('teacher_dashboard'))

# لوحة تحكم الإدارة
@app.route('/dashboard/admin')
@login_required
def admin_dashboard():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    inspectors = User.query.filter_by(role=Role.INSPECTOR).all()
    teachers = User.query.filter_by(role=Role.TEACHER).all()
    admins = User.query.filter_by(role=Role.ADMIN).all()
    user_managers = User.query.filter_by(role=Role.USER_MANAGER).all()

    # الحصول على الحسابات المعطلة (في انتظار التفعيل)
    pending_users = User.query.filter_by(_is_active=False).all()

    # الحصول على المواد الدراسية الفعلية من قواعد البيانات
    subjects_data = {}
    levels = EducationalLevel.query.all()

    # تحديد ألوان مميزة لكل مستوى تعليمي
    level_colors = {
        'السنة الأولى ابتدائي': {'color': 'primary', 'hex': '#0d6efd'},      # أزرق
        'السنة الثانية ابتدائي': {'color': 'success', 'hex': '#198754'},     # أخضر
        'السنة الثالثة ابتدائي': {'color': 'warning', 'hex': '#fd7e14'},     # برتقالي
        'السنة الرابعة ابتدائي': {'color': 'danger', 'hex': '#dc3545'},      # أحمر
        'السنة الخامسة ابتدائي': {'color': 'info', 'hex': '#0dcaf0'}        # سماوي
    }

    for level in levels:
        level_db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
        if level_db:
            subjects = LevelDataEntry.query.filter_by(
                database_id=level_db.id,
                entry_type='subject',
                is_active=True
            ).all()

            subjects_data[level.name] = {
                'subjects': [],
                'level_color': level_colors.get(level.name, {'color': 'secondary', 'hex': '#6c757d'})
            }
            for subject in subjects:
                # حساب عدد المواد المعرفية لكل مادة دراسية
                domains = LevelDataEntry.query.filter_by(
                    database_id=level_db.id,
                    entry_type='domain',
                    parent_id=subject.id,
                    is_active=True
                ).all()

                materials_count = 0
                for domain in domains:
                    materials_count += LevelDataEntry.query.filter_by(
                        database_id=level_db.id,
                        entry_type='material',
                        parent_id=domain.id,
                        is_active=True
                    ).count()

                subjects_data[level.name]['subjects'].append({
                    'id': subject.id,
                    'name': subject.name,
                    'materials_count': materials_count,
                    'level_color': level_colors.get(level.name, {'color': 'secondary', 'hex': '#6c757d'})
                })

    return render_template('admin_dashboard.html',
                         inspectors=inspectors,
                         teachers=teachers,
                         admins=admins,
                         user_managers=user_managers,
                         pending_users=pending_users,
                         subjects_data=subjects_data)

# صفحة الإدارة المتقدمة
@app.route('/admin/advanced')
@login_required
def admin_advanced():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    # حساب إحصائيات التقدم الحالية
    total_progress_entries = ProgressEntry.query.count()
    total_teachers = User.query.filter_by(role=Role.TEACHER).count()

    # حساب عدد الأساتذة الذين لديهم تقدمات
    teachers_with_progress = db.session.query(ProgressEntry.user_id).distinct().count()

    return render_template('admin_advanced.html',
                         total_progress_entries=total_progress_entries,
                         total_teachers=total_teachers,
                         teachers_with_progress=teachers_with_progress)

# تصفير عدّاد التقدم لجميع الأساتذة
@app.route('/admin/reset_all_progress', methods=['POST'])
@login_required
def reset_all_progress():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # التحقق من كلمة مرور الأدمن
        admin_password = request.form.get('admin_password')
        confirm_reset = request.form.get('confirm_reset')

        if not admin_password or not confirm_reset:
            flash('يرجى إدخال كلمة المرور وتأكيد الموافقة', 'danger')
            return redirect(url_for('admin_advanced'))

        # التحقق من صحة كلمة مرور الأدمن
        from werkzeug.security import check_password_hash
        if not check_password_hash(current_user.password_hash, admin_password):
            flash('كلمة المرور غير صحيحة', 'danger')
            return redirect(url_for('admin_advanced'))

        # حساب الإحصائيات قبل الحذف للتسجيل
        total_entries_before = ProgressEntry.query.count()
        affected_teachers = db.session.query(ProgressEntry.user_id).distinct().count()

        # حذف جميع سجلات التقدم
        deleted_count = ProgressEntry.query.delete()
        db.session.commit()

        # تسجيل العملية في السجلات (يمكن إضافة جدول للسجلات لاحقاً)
        print(f"ADMIN ACTION: {current_user.username} reset all progress data")
        print(f"- Deleted entries: {deleted_count}")
        print(f"- Affected teachers: {affected_teachers}")
        print(f"- Timestamp: {datetime.utcnow()}")

        flash(f'تم تصفير عدّاد التقدم بنجاح. تم حذف {deleted_count} سجل تقدم لـ {affected_teachers} أستاذ.', 'success')

    except Exception as e:
        db.session.rollback()
        print(f"Error resetting progress: {str(e)}")
        flash('حدث خطأ أثناء تصفير عدّاد التقدم. يرجى المحاولة مرة أخرى.', 'danger')

    return redirect(url_for('admin_advanced'))

# إدارة المفتشين والأساتذة
@app.route('/admin/manage_inspectors')
@login_required
def manage_inspectors():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على جميع المفتشين
    inspectors = User.query.filter_by(role=Role.INSPECTOR).all()

    # الحصول على جميع الأساتذة
    teachers = User.query.filter_by(role=Role.TEACHER).all()

    # إنشاء قاموس للمفتشين والأساتذة تحت إشرافهم
    inspector_data = {}
    for inspector in inspectors:
        supervised_teachers = inspector.supervised_teachers.all()
        inspector_data[inspector.id] = {
            'inspector': inspector,
            'teachers': supervised_teachers,
            'teacher_count': len(supervised_teachers)
        }

    # الأساتذة غير المرتبطين بأي مفتش
    unassigned_teachers = []
    for teacher in teachers:
        if teacher.inspectors.count() == 0:
            unassigned_teachers.append(teacher)

    return render_template('manage_inspectors.html',
                         inspector_data=inspector_data,
                         unassigned_teachers=unassigned_teachers,
                         all_inspectors=inspectors)

# تعديل المفتش المشرف على الأستاذ
@app.route('/admin/assign_teacher_to_inspector', methods=['POST'])
@login_required
def assign_teacher_to_inspector():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    teacher_id = request.form.get('teacher_id')
    inspector_id = request.form.get('inspector_id')

    if not teacher_id:
        flash('يجب اختيار أستاذ', 'danger')
        return redirect(url_for('manage_inspectors'))

    try:
        teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
        if not teacher:
            flash('الأستاذ غير موجود', 'danger')
            return redirect(url_for('manage_inspectors'))

        # إزالة الأستاذ من أي مفتش سابق
        for inspector in teacher.inspectors:
            inspector.supervised_teachers.remove(teacher)

        # إضافة الأستاذ للمفتش الجديد (إذا تم اختيار مفتش)
        if inspector_id:
            new_inspector = User.query.filter_by(id=inspector_id, role=Role.INSPECTOR).first()
            if new_inspector:
                new_inspector.supervised_teachers.append(teacher)
                flash(f'تم تعيين الأستاذ {teacher.username} تحت إشراف المفتش {new_inspector.username}', 'success')
            else:
                flash('المفتش غير موجود', 'danger')
        else:
            flash(f'تم إلغاء إشراف الأستاذ {teacher.username}', 'info')

        db.session.commit()

    except Exception as e:
        db.session.rollback()
        print(f"Error assigning teacher to inspector: {str(e)}")
        flash('حدث خطأ أثناء تعديل الإشراف', 'danger')

    return redirect(url_for('manage_inspectors'))

# إدارة قواعد البيانات المنفصلة
@app.route('/admin/databases')
@login_required
def manage_databases():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    levels = EducationalLevel.query.all()
    databases = LevelDatabase.query.all()

    return render_template('manage_level_databases.html', levels=levels, databases=databases)

# إضافة قاعدة بيانات جديدة
@app.route('/admin/databases/add', methods=['POST'])
@login_required
def add_database():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    level_id = request.form.get('level_id')
    name = request.form.get('name')
    file_path = request.form.get('file_path')
    is_active = 'is_active' in request.form

    # التحقق من وجود المستوى
    level = EducationalLevel.query.get(level_id)
    if not level:
        flash('المستوى التعليمي غير موجود', 'danger')
        return redirect(url_for('manage_databases'))

    # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
    data_dir = os.path.join(app.root_path, 'data')
    os.makedirs(data_dir, exist_ok=True)

    # إنشاء قاعدة بيانات جديدة
    new_db = LevelDatabase(
        level_id=level_id,
        name=name,
        file_path=file_path,
        is_active=is_active
    )

    db.session.add(new_db)
    db.session.commit()

    flash('تم إضافة قاعدة البيانات بنجاح', 'success')
    return redirect(url_for('manage_databases'))

# عرض قاعدة بيانات
@app.route('/admin/databases/<int:db_id>/view')
@login_required
def view_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)
    entries = LevelDataEntry.query.filter_by(database_id=db_id).all()

    return render_template('view_database.html', database=database, entries=entries)

# تعديل قاعدة بيانات
@app.route('/admin/databases/<int:db_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if request.method == 'POST':
        database.name = request.form.get('name')
        database.file_path = request.form.get('file_path')
        database.is_active = 'is_active' in request.form

        db.session.commit()

        flash('تم تحديث قاعدة البيانات بنجاح', 'success')
        return redirect(url_for('manage_databases'))

    return render_template('edit_database.html', database=database)

# حذف قاعدة بيانات
@app.route('/admin/databases/<int:db_id>/delete', methods=['POST'])
@login_required
def delete_database(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    # حذف جميع العناصر أولاً
    LevelDataEntry.query.filter_by(database_id=db_id).delete()

    # حذف قاعدة البيانات
    db.session.delete(database)
    db.session.commit()

    flash('تم حذف قاعدة البيانات بنجاح', 'success')
    return redirect(url_for('manage_databases'))

# تفعيل/تعطيل قاعدة بيانات
@app.route('/admin/databases/<int:db_id>/toggle/<string:action>')
@login_required
def toggle_database(db_id, action):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if action == 'activate':
        database.is_active = True
        flash('تم تفعيل قاعدة البيانات بنجاح', 'success')
    elif action == 'deactivate':
        database.is_active = False
        flash('تم تعطيل قاعدة البيانات بنجاح', 'success')

    db.session.commit()
    return redirect(url_for('manage_databases'))

# إدارة عناصر قاعدة البيانات
@app.route('/admin/databases/<int:db_id>/entries/add', methods=['POST'])
@login_required
def add_database_entry(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    entry_type = request.form.get('entry_type')
    parent_id = request.form.get('parent_id')
    is_active = 'is_active' in request.form
    is_multiple = 'is_multiple' in request.form

    # التحقق من نوع الإضافة (مفردة أو متعددة)
    if is_multiple:
        # إضافة متعددة
        multiple_entries = request.form.get('multiple_entries', '')
        entries_list = [entry.strip() for entry in multiple_entries.split('\n') if entry.strip()]

        # الحصول على معرفات العناصر الأب المحددة (للاختيار المتعدد)
        selected_parent_ids = request.form.get('selected_parent_ids', '')
        parent_ids_list = []

        if selected_parent_ids and selected_parent_ids.strip():
            parent_ids_list = [pid.strip() for pid in selected_parent_ids.split(',') if pid.strip() and pid.strip() != '0']
        elif parent_id and parent_id != '0':
            parent_ids_list = [parent_id]

        added_count = 0
        total_attempts = 0

        # إذا لم يتم تحديد عناصر أب وكان النوع يحتاج عنصر أب
        if entry_type != 'subject' and not parent_ids_list:
            return jsonify({
                'success': False,
                'message': 'يجب تحديد عنصر أب واحد على الأقل'
            })

        # إضافة العناصر لكل عنصر أب محدد
        for entry_name in entries_list:
            if entry_type == 'subject':
                # المواد الدراسية لا تحتاج عنصر أب
                total_attempts += 1
                existing_entry = LevelDataEntry.query.filter_by(
                    database_id=db_id,
                    entry_type=entry_type,
                    parent_id=None,
                    name=entry_name
                ).first()

                if not existing_entry:
                    new_entry = LevelDataEntry(
                        database_id=db_id,
                        entry_type=entry_type,
                        parent_id=None,
                        name=entry_name,
                        description=f"مضاف بواسطة الإضافة المتعددة",
                        is_active=is_active
                    )
                    db.session.add(new_entry)
                    added_count += 1
            else:
                # العناصر الأخرى تحتاج عنصر أب
                for parent_id_str in parent_ids_list:
                    total_attempts += 1
                    parent_id_int = int(parent_id_str) if parent_id_str.isdigit() else None

                    # التحقق من وجود العنصر
                    existing_entry = LevelDataEntry.query.filter_by(
                        database_id=db_id,
                        entry_type=entry_type,
                        parent_id=parent_id_int,
                        name=entry_name
                    ).first()

                    if not existing_entry:
                        # إنشاء عنصر جديد
                        new_entry = LevelDataEntry(
                            database_id=db_id,
                            entry_type=entry_type,
                            parent_id=parent_id_int,
                            name=entry_name,
                            description=f"مضاف بواسطة الإضافة المتعددة",
                            is_active=is_active
                        )
                        db.session.add(new_entry)
                        added_count += 1

        db.session.commit()

        # إرجاع استجابة JSON للطلبات المتعددة
        message = f'تم إضافة {added_count} عنصر بنجاح'
        if total_attempts > added_count:
            skipped = total_attempts - added_count
            message += f' (تم تجاهل {skipped} عنصر مكرر)'

        return jsonify({
            'success': True,
            'message': message,
            'count': added_count,
            'total_attempts': total_attempts
        })
    else:
        # إضافة مفردة
        name = request.form.get('name')
        description = request.form.get('description')

        # إنشاء عنصر جديد
        new_entry = LevelDataEntry(
            database_id=db_id,
            entry_type=entry_type,
            parent_id=parent_id if parent_id != '0' else None,
            name=name,
            description=description,
            is_active=is_active
        )

        db.session.add(new_entry)
        db.session.commit()

        flash('تم إضافة العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

@app.route('/admin/databases/<int:db_id>/entries/<int:entry_id>/edit', methods=['POST'])
@login_required
def edit_database_entry(db_id, entry_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    entry = LevelDataEntry.query.get_or_404(entry_id)

    entry.name = request.form.get('name')
    entry.description = request.form.get('description')
    entry.is_active = 'is_active' in request.form

    db.session.commit()

    flash('تم تحديث العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

@app.route('/admin/databases/<int:db_id>/entries/<int:entry_id>/delete', methods=['POST'])
@login_required
def delete_database_entry(db_id, entry_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    entry = LevelDataEntry.query.get_or_404(entry_id)

    # حذف جميع العناصر الفرعية بشكل متكرر
    delete_child_entries(db_id, entry_id)

    # حذف العنصر
    db.session.delete(entry)
    db.session.commit()

    flash('تم حذف العنصر بنجاح', 'success')
    return redirect(url_for('view_database', db_id=db_id))

# دالة مساعدة لحذف العناصر الفرعية بشكل متكرر
def delete_child_entries(db_id, parent_id):
    child_entries = LevelDataEntry.query.filter_by(database_id=db_id, parent_id=parent_id).all()

    for entry in child_entries:
        # حذف أبناء هذا العنصر
        delete_child_entries(db_id, entry.id)

        # حذف هذا العنصر
        db.session.delete(entry)

# لوحة تحكم المفتش (محسنة للأداء)
@app.route('/dashboard/inspector')
@login_required
def inspector_dashboard():
    try:
        # التحقق من المستخدم الحالي
        print(f"Current user: {current_user.username} (ID: {current_user.id}, Role: {current_user.role})")

        if current_user.role != Role.INSPECTOR:
            flash('غير مصرح بالوصول', 'danger')
            return redirect(url_for('dashboard'))

        # الحصول على الأساتذة الذين يشرف عليهم المفتش (محسن)
        try:
            # استعلام محسن مع تحميل البيانات المطلوبة مسبقاً
            teachers = db.session.query(User).join(
                inspector_teacher,
                User.id == inspector_teacher.c.teacher_id
            ).filter(
                inspector_teacher.c.inspector_id == current_user.id,
                User.role == Role.TEACHER
            ).all()
            print(f"Found {len(teachers)} supervised teachers for inspector {current_user.username}")
        except Exception as e:
            print(f"Error fetching supervised teachers: {str(e)}")
            teachers = []

        # الحصول على الأساتذة المتاحين للإضافة (محسن)
        try:
            supervised_teacher_ids = [t.id for t in teachers]
            if supervised_teacher_ids:
                available_teachers = User.query.filter(
                    User.role == Role.TEACHER,
                    ~User.id.in_(supervised_teacher_ids)
                ).all()
            else:
                available_teachers = User.query.filter_by(role=Role.TEACHER).all()
            print(f"Found {len(available_teachers)} available teachers for inspector {current_user.username}")
        except Exception as e:
            print(f"Error fetching available teachers: {str(e)}")
            available_teachers = []

        # حساب بيانات التقدم بشكل محسن (استعلام واحد لجميع الأساتذة)
        teacher_progress = {}
        progress_stats = {
            'completed': 0,
            'in_progress': 0,
            'planned': 0,
            'total': 0,
            'completed_materials': 0,
            'total_materials': 0
        }

        if teachers:
            supervised_teacher_ids = [t.id for t in teachers]

            # استعلام محسن لجميع سجلات التقدم
            all_entries = ProgressEntry.query.filter(
                ProgressEntry.user_id.in_(supervised_teacher_ids)
            ).all()

            # تجميع البيانات حسب المعلم
            entries_by_teacher = {}
            for entry in all_entries:
                if entry.user_id not in entries_by_teacher:
                    entries_by_teacher[entry.user_id] = []
                entries_by_teacher[entry.user_id].append(entry)

            # معالجة كل أستاذ
            for teacher in teachers:
                try:
                    # حساب التقدم بالطريقة الجديدة (بناءً على المواد المعرفية)
                    teacher_progress_data = calculate_progress_by_materials(teacher.id)

                    # الحصول على سجلات التقدم للأستاذ
                    entries = entries_by_teacher.get(teacher.id, [])
                    print(f"Found {len(entries)} progress entries for teacher {teacher.username}")

                    # حساب الإحصائيات
                    stats = {
                        'completed': 0,
                        'in_progress': 0,
                        'planned': 0,
                        'total': len(entries)
                    }

                    for entry in entries:
                        if entry.status in stats:
                            stats[entry.status] += 1
                            progress_stats[entry.status] += 1

                    progress_stats['total'] += stats['total']

                    # استخدام نسبة الإنجاز الجديدة المبنية على المواد المعرفية
                    completion_rate = teacher_progress_data['completion_rate']

                    # إضافة إحصائيات المواد المعرفية
                    progress_stats['completed_materials'] += teacher_progress_data['completed_materials']
                    progress_stats['total_materials'] += teacher_progress_data['total_materials']

                    # الحصول على سجلات التقدم الأخيرة (محسن)
                    recent_entries = sorted(entries, key=lambda x: x.date, reverse=True)[:5]
                    detailed_entries = []
                except Exception as e:
                    print(f"Error processing teacher {teacher.id}: {str(e)}")
                    stats = {'completed': 0, 'in_progress': 0, 'planned': 0, 'total': 0}
                    completion_rate = 0
                    recent_entries = []
                    detailed_entries = []

                # معالجة محسنة للتفاصيل (تقليل الاستعلامات)
                for entry in recent_entries:
                    entry_details = {
                        'id': entry.id,
                        'date': entry.date,
                        'status': entry.status,
                        'notes': entry.notes,
                        'level': None,
                        'subject': None,
                        'domain': None,
                        'material': None,
                        'competency': None
                    }

                    # معالجة مبسطة للكفاءة (تقليل الاستعلامات)
                    if entry.competency_id:
                        try:
                            # محاولة الحصول على الكفاءة من نموذج LevelDataEntry
                            competency = LevelDataEntry.query.filter_by(id=entry.competency_id, entry_type='competency').first()
                            if competency:
                                entry_details['competency'] = {
                                    'id': competency.id,
                                    'name': competency.name,
                                    'description': competency.description
                                }
                                # تبسيط الحصول على التفاصيل الأخرى
                                if competency.parent_id:
                                    material = LevelDataEntry.query.filter_by(id=competency.parent_id, entry_type='material').first()
                                    if material:
                                        entry_details['material'] = {'id': material.id, 'name': material.name}
                        except Exception as e:
                            print(f"Error processing competency details: {str(e)}")
                            continue

                    detailed_entries.append(entry_details)

            teacher_progress[teacher.id] = {
                'stats': stats,
                'completion_rate': completion_rate,
                'recent_entries': detailed_entries,
                'completed_materials': teacher_progress_data['completed_materials'],
                'total_materials': teacher_progress_data['total_materials'],
                'completed_by_subject': teacher_progress_data['completed_by_subject'],
                'total_by_subject': teacher_progress_data['total_by_subject']
            }
            print(f"Added progress data for teacher {teacher.username} with completion rate {completion_rate}%")

        # حساب نسبة الإنجاز الإجمالية (محسن للأداء)
        if len(teachers) > 0:
            supervised_teacher_ids = [teacher.id for teacher in teachers]
            total_materials = ProgressEntry.query.filter(
                ProgressEntry.user_id.in_(supervised_teacher_ids)
            ).count()
            completed_materials = ProgressEntry.query.filter(
                ProgressEntry.user_id.in_(supervised_teacher_ids),
                ProgressEntry.status == 'completed'
            ).count()

            if total_materials > 0:
                overall_completion_rate = (completed_materials / total_materials) * 100
            else:
                overall_completion_rate = 0
        else:
            overall_completion_rate = 0

        # حساب نسبة الإنجاز القديمة للمقارنة
        old_completion_rate = 0
        if progress_stats['total'] > 0:
            old_completion_rate = (progress_stats['completed'] / progress_stats['total']) * 100

        # حساب إحصائيات المستويات (محسن للأداء)
        levels = EducationalLevel.query.all()
        level_stats = {}

        if teachers:
            supervised_teacher_ids = [t.id for t in teachers]

            # استعلام محسن للجداول
            schedules = Schedule.query.filter(
                Schedule.user_id.in_(supervised_teacher_ids)
            ).all()

            # تجميع الجداول حسب المستوى
            schedules_by_level = {}
            for schedule in schedules:
                if schedule.level_id not in schedules_by_level:
                    schedules_by_level[schedule.level_id] = []
                schedules_by_level[schedule.level_id].append(schedule)

            for level in levels:
                level_schedules = schedules_by_level.get(level.id, [])
                teacher_ids = [s.user_id for s in level_schedules]

                # حساب التقدم بناءً على المواد المعرفية للمستوى
                level_completed_materials = 0
                level_total_materials = 0

                # الحصول على سجلات التقدم للأساتذة المرتبطين بهذا المستوى
                level_entries = [entry for entry in all_entries if entry.user_id in teacher_ids] if teacher_ids else []

                # حساب الإحصائيات
                stats = {
                    'completed': 0,
                    'in_progress': 0,
                    'planned': 0,
                    'total': len(level_entries)
                }

                for entry in level_entries:
                    if entry.status in stats:
                        stats[entry.status] += 1

                # حساب التقدم بالطريقة الجديدة لكل أستاذ في هذا المستوى
                for teacher_id in teacher_ids:
                    level_progress = calculate_level_progress_by_materials(teacher_id, level.id)
                    level_completed_materials += level_progress['completed_materials']
                    level_total_materials += level_progress['total_materials']

                # حساب نسبة الإنجاز بناءً على المواد المعرفية
                completion_rate = 0
                if level_total_materials > 0:
                    completion_rate = (level_completed_materials / level_total_materials) * 100

                level_stats[level.id] = {
                    'name': level.name,
                    'stats': stats,
                    'completion_rate': completion_rate,
                    'completed_materials': level_completed_materials,
                    'total_materials': level_total_materials
                }

        # حساب إحصائيات المواد الدراسية
        subject_stats = {}
        for teacher_id, teacher_data in teacher_progress.items():
            if 'completed_by_subject' in teacher_data and 'total_by_subject' in teacher_data:
                for subject_name, completed in teacher_data['completed_by_subject'].items():
                    total = teacher_data['total_by_subject'].get(subject_name, 0)

                    if subject_name not in subject_stats:
                        subject_stats[subject_name] = {
                            'name': subject_name,
                            'completed_materials': 0,
                            'total_materials': 0,
                            'completion_rate': 0
                        }

                    subject_stats[subject_name]['completed_materials'] += completed
                    subject_stats[subject_name]['total_materials'] += total

        # حساب نسبة الإنجاز لكل مادة
        for subject_name in subject_stats:
            if subject_stats[subject_name]['total_materials'] > 0:
                subject_stats[subject_name]['completion_rate'] = (
                    subject_stats[subject_name]['completed_materials'] /
                    subject_stats[subject_name]['total_materials']
                ) * 100

        # حساب البيانات الجديدة (محسن للأداء)
        from datetime import date
        today = date.today()

        if teachers:
            supervised_teacher_ids = [teacher.id for teacher in teachers]

            # حساب الدروس المنجزة اليوم (من البيانات المحملة مسبقاً)
            today_completed_lessons = sum(1 for entry in all_entries
                                        if entry.status == 'completed' and entry.date == today)

            # حساب الدروس قيد التنفيذ (من البيانات المحملة مسبقاً)
            total_in_progress_lessons = sum(1 for entry in all_entries
                                          if entry.status == 'in_progress')

            # حساب متوسط التقدم اليومي
            first_day_of_month = date(today.year, today.month, 1)
            days_in_month = (today - first_day_of_month).days + 1
            total_completed_materials = sum(1 for entry in all_entries
                                          if entry.status == 'completed')
            daily_average_progress = total_completed_materials / days_in_month if days_in_month > 0 else 0

            # حساب أفضل أستاذ هذا الشهر (محسن)
            if today.month < 12:
                first_day_next_month = date(today.year, today.month + 1, 1)
            else:
                first_day_next_month = date(today.year + 1, 1, 1)

            teacher_monthly_counts = {}
            for entry in all_entries:
                if (entry.status == 'completed' and
                    entry.date >= first_day_of_month and
                    entry.date < first_day_next_month):
                    teacher_monthly_counts[entry.user_id] = teacher_monthly_counts.get(entry.user_id, 0) + 1

            best_teacher = None
            max_completed = 0
            for teacher in teachers:
                completed_this_month = teacher_monthly_counts.get(teacher.id, 0)
                if completed_this_month > max_completed:
                    max_completed = completed_this_month
                    best_teacher = {
                        'name': teacher.username,
                        'completed_count': completed_this_month
                    }

            best_teacher_this_month = best_teacher

            # حساب الإشعارات غير المقروءة
            unread_notifications_count = get_unread_notifications_count_for_user(current_user.id)
        else:
            today_completed_lessons = 0
            total_in_progress_lessons = 0
            daily_average_progress = 0
            best_teacher_this_month = None
            unread_notifications_count = 0

        # إضافة التاريخ الحالي
        current_date = today.strftime('%Y-%m-%d')

        # طباعة معلومات التصحيح قبل عرض القالب
        print(f"Final data for template:")
        print(f"- Teachers count: {len(teachers)}")
        print(f"- Available teachers count: {len(available_teachers)}")
        print(f"- Teacher progress keys: {list(teacher_progress.keys()) if teacher_progress else 'None'}")
        print(f"- Progress stats: {progress_stats}")
        print(f"- Overall completion rate: {overall_completion_rate}%")
        print(f"- Today completed lessons: {today_completed_lessons}")
        print(f"- Total in progress lessons: {total_in_progress_lessons}")
        print(f"- Daily average progress: {daily_average_progress}")
        print(f"- Best teacher this month: {best_teacher_this_month}")
        print(f"- Unread notifications count: {unread_notifications_count}")
        print(f"- Level stats keys: {list(level_stats.keys()) if level_stats else 'None'}")

        return render_template('inspector_dashboard.html',
                               teachers=teachers,
                               available_teachers=available_teachers,
                               teacher_progress=teacher_progress,
                               progress_stats=progress_stats,
                               overall_completion_rate=overall_completion_rate,
                               level_stats=level_stats,
                               subject_stats=subject_stats,
                               today_completed_lessons=today_completed_lessons,
                               total_in_progress_lessons=total_in_progress_lessons,
                               daily_average_progress=daily_average_progress,
                               best_teacher_this_month=best_teacher_this_month,
                               unread_notifications_count=unread_notifications_count,
                               current_date=current_date)

    except Exception as e:
        print(f"Error in inspector_dashboard: {str(e)}")
        flash('حدث خطأ أثناء تحميل لوحة التحكم', 'danger')
        # تمرير قيم افتراضية لجميع المتغيرات المطلوبة
        from datetime import date
        current_date = date.today().strftime('%Y-%m-%d')

        return render_template('inspector_dashboard.html',
                               teachers=[],
                               available_teachers=[],
                               teacher_progress={},
                               progress_stats={'completed': 0, 'in_progress': 0, 'planned': 0, 'total': 0},
                               overall_completion_rate=0,
                               level_stats={},
                               subject_stats={},
                               today_completed_lessons=0,
                               total_in_progress_lessons=0,
                               daily_average_progress=0,
                               best_teacher_this_month=None,
                               unread_notifications_count=0,
                               current_date=current_date)

# إضافة أستاذ تحت إشراف المفتش
@app.route('/inspector/add-teacher', methods=['POST'])
@login_required
def add_teacher_to_supervision():
    if current_user.role != Role.INSPECTOR:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    teacher_id = request.form.get('teacher_id')

    if not teacher_id:
        flash('يجب اختيار أستاذ', 'danger')
        return redirect(url_for('inspector_dashboard'))

    try:
        # التحقق من وجود الأستاذ
        teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
        if not teacher:
            flash('الأستاذ غير موجود', 'danger')
            return redirect(url_for('inspector_dashboard'))

        # التحقق من أن الأستاذ ليس تحت إشراف مفتش آخر
        if teacher.inspectors.count() > 0:
            existing_inspector = teacher.inspectors.first()
            if existing_inspector.id != current_user.id:
                flash(f'هذا الأستاذ تحت إشراف المفتش {existing_inspector.username}', 'warning')
                return redirect(url_for('inspector_dashboard'))

        # إضافة الأستاذ تحت إشراف المفتش الحالي
        if teacher not in current_user.supervised_teachers:
            current_user.supervised_teachers.append(teacher)
            db.session.commit()
            flash(f'تم إضافة الأستاذ {teacher.username} تحت إشرافك بنجاح', 'success')
        else:
            flash('الأستاذ بالفعل تحت إشرافك', 'info')

    except Exception as e:
        db.session.rollback()
        print(f"Error adding teacher to supervision: {str(e)}")
        flash('حدث خطأ أثناء إضافة الأستاذ', 'danger')

    return redirect(url_for('inspector_dashboard'))

# حذف أستاذ من إشراف المفتش
@app.route('/inspector/remove-teacher/<int:teacher_id>', methods=['POST'])
@login_required
def remove_teacher_from_supervision(teacher_id):
    if current_user.role != Role.INSPECTOR:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # التحقق من وجود الأستاذ تحت إشراف المفتش الحالي
        teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
        if not teacher or teacher not in current_user.supervised_teachers:
            flash('الأستاذ غير موجود تحت إشرافك', 'danger')
            return redirect(url_for('inspector_dashboard'))

        # إزالة الأستاذ من الإشراف
        current_user.supervised_teachers.remove(teacher)
        db.session.commit()

        flash(f'تم إزالة الأستاذ {teacher.username} من إشرافك', 'success')

    except Exception as e:
        db.session.rollback()
        print(f"Error removing teacher from supervision: {str(e)}")
        flash('حدث خطأ أثناء إزالة الأستاذ', 'danger')

    return redirect(url_for('inspector_dashboard'))

# عرض تقدم أستاذ معين للمفتش
@app.route('/inspector/teacher_progress/<int:teacher_id>')
@login_required
def get_teacher_progress(teacher_id):
    if current_user.role != Role.INSPECTOR:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403

    try:
        # التحقق من أن الأستاذ تحت إشراف المفتش الحالي
        teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
        if not teacher or teacher not in current_user.supervised_teachers:
            return jsonify({'error': 'الأستاذ غير موجود تحت إشرافك'}), 404

        # الحصول على تقدم الأستاذ
        progress_data = calculate_progress_by_materials(teacher_id)

        # الحصول على آخر 10 سجلات تقدم
        recent_progress = ProgressEntry.query.filter_by(user_id=teacher_id)\
                                           .order_by(ProgressEntry.date.desc())\
                                           .limit(10).all()

        # تحويل البيانات إلى JSON
        progress_list = []
        for entry in recent_progress:
            progress_list.append({
                'date': entry.date.strftime('%Y-%m-%d') if entry.date else '',
                'level': entry.level.name if entry.level else 'غير محدد',
                'subject': entry.subject.name if entry.subject else 'غير محدد',
                'domain': entry.domain.name if entry.domain else 'غير محدد',
                'material': entry.material.name if entry.material else 'غير محدد',
                'status': entry.status,
                'status_text': {
                    'completed': 'مكتمل',
                    'in_progress': 'قيد التنفيذ',
                    'planned': 'مخطط'
                }.get(entry.status, entry.status)
            })

        return jsonify({
            'teacher_name': teacher.username,
            'teacher_email': teacher.email,
            'overall_progress': progress_data,
            'recent_progress': progress_list
        })

    except Exception as e:
        print(f"Error getting teacher progress: {str(e)}")
        return jsonify({'error': 'حدث خطأ أثناء تحميل البيانات'}), 500

# عرض ملف الأستاذ الشخصي للمفتش
@app.route('/inspector/teacher_profile/<int:teacher_id>')
@login_required
def get_teacher_profile(teacher_id):
    if current_user.role != Role.INSPECTOR:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403

    try:
        # التحقق من أن الأستاذ تحت إشراف المفتش الحالي
        teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
        if not teacher or teacher not in current_user.supervised_teachers:
            return jsonify({'error': 'الأستاذ غير موجود تحت إشرافك'}), 404

        # الحصول على إحصائيات الأستاذ
        total_progress = ProgressEntry.query.filter_by(user_id=teacher_id).count()
        completed_progress = ProgressEntry.query.filter_by(user_id=teacher_id, status='completed').count()

        # آخر نشاط
        last_activity = ProgressEntry.query.filter_by(user_id=teacher_id)\
                                         .order_by(ProgressEntry.date.desc())\
                                         .first()

        return jsonify({
            'teacher_name': teacher.username,
            'teacher_email': teacher.email,
            'created_at': teacher.created_at.strftime('%Y-%m-%d') if teacher.created_at else 'غير محدد',
            'total_progress': total_progress,
            'completed_progress': completed_progress,
            'completion_rate': round((completed_progress / total_progress * 100), 2) if total_progress > 0 else 0,
            'last_activity': last_activity.date.strftime('%Y-%m-%d') if last_activity and last_activity.date else 'لا يوجد نشاط'
        })

    except Exception as e:
        print(f"Error getting teacher profile: {str(e)}")
        return jsonify({'error': 'حدث خطأ أثناء تحميل البيانات'}), 500

# دالة حساب الدروس المنجزة اليوم لجميع الأساتذة تحت الإشراف
def get_today_completed_lessons(inspector_id):
    """
    حساب عدد الدروس المكتملة اليوم لجميع الأساتذة تحت إشراف المفتش
    """
    try:
        from datetime import date
        today = date.today()

        # الحصول على الأساتذة تحت إشراف المفتش
        inspector = User.query.get(inspector_id)
        if not inspector:
            return 0

        supervised_teacher_ids = [teacher.id for teacher in inspector.supervised_teachers]
        if not supervised_teacher_ids:
            return 0

        # حساب الدروس المكتملة اليوم
        completed_today = ProgressEntry.query.filter(
            ProgressEntry.user_id.in_(supervised_teacher_ids),
            ProgressEntry.status == 'completed',
            ProgressEntry.date == today
        ).count()

        return completed_today

    except Exception as e:
        print(f"Error calculating today's completed lessons: {str(e)}")
        return 0

# دالة حساب إجمالي الدروس قيد التنفيذ لجميع الأساتذة تحت الإشراف
def get_total_in_progress_lessons(inspector_id):
    """
    حساب عدد الدروس قيد التنفيذ لجميع الأساتذة تحت إشراف المفتش
    """
    try:
        # الحصول على الأساتذة تحت إشراف المفتش
        inspector = User.query.get(inspector_id)
        if not inspector:
            return 0

        supervised_teacher_ids = [teacher.id for teacher in inspector.supervised_teachers]
        if not supervised_teacher_ids:
            return 0

        # حساب الدروس قيد التنفيذ
        in_progress_total = ProgressEntry.query.filter(
            ProgressEntry.user_id.in_(supervised_teacher_ids),
            ProgressEntry.status == 'in_progress'
        ).count()

        return in_progress_total

    except Exception as e:
        print(f"Error calculating total in progress lessons: {str(e)}")
        return 0

# دالة حساب نسبة الإنجاز الإجمالية لجميع الأساتذة تحت الإشراف
def calculate_overall_completion_rate_for_inspector(inspector_id):
    """
    حساب نسبة الإنجاز الإجمالية كمعدل لجميع الأساتذة تحت إشراف المفتش
    الدالة مرنة وتتكيف مع إضافة أو حذف الأساتذة
    """
    try:
        # الحصول على الأساتذة تحت إشراف المفتش
        inspector = User.query.get(inspector_id)
        if not inspector:
            return 0.0

        teachers = inspector.supervised_teachers.all()
        if not teachers:
            return 0.0

        total_completion_rates = []

        for teacher in teachers:
            # حساب نسبة الإنجاز لكل أستاذ
            teacher_progress = calculate_progress_by_materials(teacher.id)
            completion_rate = teacher_progress.get('completion_rate', 0)
            total_completion_rates.append(completion_rate)

        # حساب المعدل
        if total_completion_rates:
            overall_rate = sum(total_completion_rates) / len(total_completion_rates)
            return round(overall_rate, 2)
        else:
            return 0.0

    except Exception as e:
        print(f"Error calculating overall completion rate for inspector: {str(e)}")
        return 0.0

# دالة حساب متوسط التقدم اليومي للمواد المعرفية
def calculate_daily_average_progress(inspector_id):
    """
    حساب متوسط التقدم اليومي للمواد المعرفية لجميع الأساتذة تحت الإشراف
    المعادلة مرنة وتتكيف مع إضافة أو حذف الأساتذة
    """
    try:
        from datetime import date, timedelta

        # الحصول على الأساتذة تحت إشراف المفتش
        inspector = User.query.get(inspector_id)
        if not inspector:
            return 0.0

        teachers = inspector.supervised_teachers.all()
        if not teachers:
            return 0.0

        # حساب إجمالي المواد المعرفية المكتملة لجميع الأساتذة (محسن للأداء)
        supervised_teacher_ids = [teacher.id for teacher in teachers]
        total_completed_materials = ProgressEntry.query.filter(
            ProgressEntry.user_id.in_(supervised_teacher_ids),
            ProgressEntry.status == 'completed'
        ).count()

        # حساب عدد الأيام منذ بداية الشهر الحالي
        today = date.today()
        first_day_of_month = date(today.year, today.month, 1)
        days_in_month = (today - first_day_of_month).days + 1

        # حساب المتوسط اليومي
        if days_in_month > 0:
            daily_average = total_completed_materials / days_in_month
            return round(daily_average, 1)
        else:
            return 0.0

    except Exception as e:
        print(f"Error calculating daily average progress: {str(e)}")
        return 0.0

# دالة تحديد أفضل أستاذ هذا الشهر
def get_best_teacher_this_month(inspector_id):
    """
    تحديد الأستاذ الذي قدم أكبر عدد من الدروس المكتملة هذا الشهر
    """
    try:
        from datetime import date

        # الحصول على الأساتذة تحت إشراف المفتش
        inspector = User.query.get(inspector_id)
        if not inspector:
            return None

        teachers = inspector.supervised_teachers.all()
        if not teachers:
            return None

        # تحديد بداية ونهاية الشهر الحالي
        today = date.today()
        first_day_of_month = date(today.year, today.month, 1)

        # إذا كان الشهر التالي في نفس السنة
        if today.month < 12:
            first_day_next_month = date(today.year, today.month + 1, 1)
        else:
            first_day_next_month = date(today.year + 1, 1, 1)

        best_teacher = None
        max_completed = 0

        for teacher in teachers:
            # حساب عدد الدروس المكتملة هذا الشهر للأستاذ
            completed_this_month = ProgressEntry.query.filter(
                ProgressEntry.user_id == teacher.id,
                ProgressEntry.status == 'completed',
                ProgressEntry.date >= first_day_of_month,
                ProgressEntry.date < first_day_next_month
            ).count()

            if completed_this_month > max_completed:
                max_completed = completed_this_month
                best_teacher = {
                    'name': teacher.username,
                    'completed_count': completed_this_month
                }

        return best_teacher

    except Exception as e:
        print(f"Error getting best teacher this month: {str(e)}")
        return None

# دالة حساب الإشعارات غير المقروءة
def get_unread_notifications_count(inspector_id):
    """
    حساب عدد الإشعارات غير المقروءة للمفتش
    """
    try:
        # هنا يمكن إضافة منطق الإشعارات عندما يتم تطوير نظام الإشعارات
        # حالياً سنرجع رقم تجريبي أو 0

        # يمكن أن تكون الإشعارات مثل:
        # - تحديثات جديدة من الأساتذة
        # - تقارير تحتاج مراجعة
        # - رسائل من الإدارة

        # للآن سنرجع عدد الأساتذة الذين لديهم تحديثات جديدة اليوم
        from datetime import date
        today = date.today()

        inspector = User.query.get(inspector_id)
        if not inspector:
            return 0

        supervised_teacher_ids = [teacher.id for teacher in inspector.supervised_teachers]
        if not supervised_teacher_ids:
            return 0

        # حساب عدد الأساتذة الذين لديهم تحديثات اليوم
        teachers_with_updates_today = ProgressEntry.query.filter(
            ProgressEntry.user_id.in_(supervised_teacher_ids),
            ProgressEntry.date == today
        ).distinct(ProgressEntry.user_id).count()

        return teachers_with_updates_today

    except Exception as e:
        print(f"Error getting unread notifications count: {str(e)}")
        return 0

# دالة حساب الإشعارات غير المقروءة للمستخدم الحالي (موحدة)
def get_unread_notifications_count_for_user(user_id):
    """
    حساب عدد الإشعارات غير المقروءة للمستخدم (جميع الأنواع)
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return 0

        direct_unread = 0

        # حساب الإشعارات المباشرة حسب دور المستخدم
        if user.role == Role.INSPECTOR:
            # المفتش: إشعارات من الإدارة
            direct_unread = AdminInspectorNotification.query.filter(
                AdminInspectorNotification.receiver_id == user_id,
                AdminInspectorNotification.is_read == False
            ).count()
        elif user.role == Role.TEACHER:
            # الأستاذ: إشعارات من المفتشين
            direct_unread = InspectorTeacherNotification.query.filter(
                InspectorTeacherNotification.receiver_id == user_id,
                InspectorTeacherNotification.is_read == False
            ).count()
        # الإدارة لا تتلقى إشعارات مباشرة (أو يمكن إضافة نوع جديد لاحقاً)

        # إشعارات عامة غير مقروءة (محسن)
        general_unread = 0
        try:
            # الحصول على معرفات الإشعارات العامة المقروءة للمستخدم
            read_notification_ids = db.session.query(GeneralNotificationRead.notification_id).filter(
                GeneralNotificationRead.user_id == user_id
            ).subquery()

            # حساب الإشعارات العامة غير المقروءة
            general_unread = GeneralNotification.query.filter(
                db.or_(
                    GeneralNotification.target_type == 'all',
                    db.and_(
                        GeneralNotification.target_type == 'role',
                        GeneralNotification.target_role == user.role
                    )
                ),
                ~GeneralNotification.id.in_(read_notification_ids)
            ).count()
        except Exception as e:
            print(f"Error calculating general notifications: {str(e)}")
            general_unread = 0

        return direct_unread + general_unread

    except Exception as e:
        print(f"Error calculating unread notifications for user {user_id}: {str(e)}")
        return 0

# صفحة إرسال الإشعارات
@app.route('/send_notification', methods=['GET', 'POST'])
@login_required
def send_notification():
    # التحقق من الصلاحيات (الإدارة ومدير المستخدمين والمفتشين)
    if current_user.role not in [Role.ADMIN, Role.USER_MANAGER, Role.INSPECTOR]:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        title = request.form.get('title')
        message = request.form.get('message')
        target_type = request.form.get('target_type')  # 'all', 'role', 'specific'
        target_role = request.form.get('target_role')
        specific_users = request.form.getlist('specific_users')

        if not title or not message:
            flash('يجب ملء العنوان والرسالة', 'danger')
            return redirect(url_for('send_notification'))

        try:

            if target_type == 'specific':
                # إرسال إشعارات فردية
                for user_id in specific_users:
                    if current_user.role in [Role.ADMIN, Role.USER_MANAGER]:
                        # الإدارة ومدير المستخدمين يمكنهما إرسال لأي شخص
                        notification = InspectorTeacherNotification(
                            sender_id=current_user.id,
                            receiver_id=int(user_id),
                            title=title,
                            message=message
                        )
                    else:
                        # المفتش يرسل للأساتذة تحت إشرافه فقط
                        teacher = User.query.get(int(user_id))
                        if teacher and teacher in current_user.supervised_teachers:
                            notification = InspectorTeacherNotification(
                                sender_id=current_user.id,
                                receiver_id=int(user_id),
                                title=title,
                                message=message
                            )
                        else:
                            continue

                    db.session.add(notification)
            else:
                # إرسال إشعار عام
                notification = GeneralNotification(
                    sender_id=current_user.id,
                    title=title,
                    message=message,
                    target_type=target_type,
                    target_role=target_role if target_type == 'role' else None
                )
                db.session.add(notification)

            db.session.commit()
            flash('تم إرسال الإشعار بنجاح', 'success')
            return redirect(url_for('send_notification'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إرسال الإشعار: {str(e)}', 'danger')
            return redirect(url_for('send_notification'))

    # الحصول على قائمة المستخدمين حسب الدور
    users_by_role = {}
    if current_user.role == Role.ADMIN:
        # الإدارة ترى جميع المستخدمين
        users_by_role['inspectors'] = User.query.filter_by(role=Role.INSPECTOR).all()
        users_by_role['teachers'] = User.query.filter_by(role=Role.TEACHER).all()
    elif current_user.role == Role.USER_MANAGER:
        # مدير المستخدمين يرى جميع المفتشين والأساتذة
        users_by_role['inspectors'] = User.query.filter_by(role=Role.INSPECTOR).all()
        users_by_role['teachers'] = User.query.filter_by(role=Role.TEACHER).all()
    else:
        # المفتش يرى الأساتذة تحت إشرافه فقط
        users_by_role['teachers'] = current_user.supervised_teachers.all()

    return render_template('send_notification.html', users_by_role=users_by_role)

# صفحة عرض الإشعارات الموحدة مع تبويبات
@app.route('/notifications')
@login_required
def view_notifications():
    # تنظيم الإشعارات حسب النوع والدور
    notifications_data = {
        'admin_notifications': [],
        'inspector_notifications': [],
        'sent_notifications': [],
        'general_notifications': []
    }

    if current_user.role == Role.INSPECTOR:
        # المفتش: الواردة من الإدارة
        admin_notifications = AdminInspectorNotification.query.filter(
            AdminInspectorNotification.receiver_id == current_user.id
        ).order_by(AdminInspectorNotification.created_at.desc()).all()

        for notif in admin_notifications:
            notif.notification_type = 'admin_to_inspector'
        notifications_data['admin_notifications'] = admin_notifications

        # المفتش: المرسلة للأساتذة
        sent_notifications = InspectorTeacherNotification.query.filter(
            InspectorTeacherNotification.sender_id == current_user.id
        ).order_by(InspectorTeacherNotification.created_at.desc()).all()

        for notif in sent_notifications:
            notif.notification_type = 'sent_to_teacher'
        notifications_data['sent_notifications'] = sent_notifications

    elif current_user.role == Role.TEACHER:
        # الأستاذ: الواردة من الإدارة (إذا وجدت)
        admin_notifications = AdminInspectorNotification.query.filter(
            AdminInspectorNotification.receiver_id == current_user.id
        ).order_by(AdminInspectorNotification.created_at.desc()).all()

        for notif in admin_notifications:
            notif.notification_type = 'admin_to_teacher'
        notifications_data['admin_notifications'] = admin_notifications

        # الأستاذ: الواردة من المفتشين
        inspector_notifications = InspectorTeacherNotification.query.filter(
            InspectorTeacherNotification.receiver_id == current_user.id
        ).order_by(InspectorTeacherNotification.created_at.desc()).all()

        for notif in inspector_notifications:
            notif.notification_type = 'inspector_to_teacher'
        notifications_data['inspector_notifications'] = inspector_notifications

    elif current_user.role == Role.ADMIN:
        # الإدارة: المرسلة للمفتشين
        sent_notifications = AdminInspectorNotification.query.filter(
            AdminInspectorNotification.sender_id == current_user.id
        ).order_by(AdminInspectorNotification.created_at.desc()).all()

        for notif in sent_notifications:
            notif.notification_type = 'sent_to_inspector'
        notifications_data['sent_notifications'] = sent_notifications

    # الإشعارات العامة (للجميع)
    general_notifications = GeneralNotification.query.filter(
        db.or_(
            GeneralNotification.target_type == 'all',
            db.and_(
                GeneralNotification.target_type == 'role',
                GeneralNotification.target_role == current_user.role
            )
        )
    ).order_by(GeneralNotification.created_at.desc()).all()

    # إضافة معلومات القراءة للإشعارات العامة
    for notification in general_notifications:
        read_record = GeneralNotificationRead.query.filter(
            GeneralNotificationRead.notification_id == notification.id,
            GeneralNotificationRead.user_id == current_user.id
        ).first()
        notification.is_read_by_user = bool(read_record)

    notifications_data['general_notifications'] = general_notifications

    return render_template('notifications.html',
                         notifications_data=notifications_data,
                         current_user_role=current_user.role)

# تحديد إشعار كمقروء (موحد لجميع الأنواع)
@app.route('/mark_notification_read/<int:notification_id>/<notification_type>')
@login_required
def mark_notification_read(notification_id, notification_type):
    try:
        if notification_type in ['admin_to_inspector', 'admin_to_teacher']:
            # إشعار من الإدارة للمفتش أو للأستاذ
            notification = AdminInspectorNotification.query.get_or_404(notification_id)
            if notification.receiver_id == current_user.id:
                notification.is_read = True
                db.session.commit()
        elif notification_type in ['inspector_to_teacher', 'sent_to_teacher']:
            # إشعار من المفتش للأستاذ (واردة أو مرسلة)
            notification = InspectorTeacherNotification.query.get_or_404(notification_id)
            if notification.receiver_id == current_user.id or notification.sender_id == current_user.id:
                notification.is_read = True
                db.session.commit()
        elif notification_type == 'sent_to_inspector':
            # إشعار مرسل من الإدارة للمفتش (للعرض فقط)
            notification = AdminInspectorNotification.query.get_or_404(notification_id)
            if notification.sender_id == current_user.id:
                # الإشعارات المرسلة لا تحتاج تحديد كمقروءة من المرسل
                pass
        elif notification_type == 'general':
            # إشعار عام
            notification = GeneralNotification.query.get_or_404(notification_id)

            # التحقق من أن الإشعار موجه للمستخدم
            if (notification.target_type == 'all' or
                (notification.target_type == 'role' and notification.target_role == current_user.role)):

                # إضافة سجل قراءة إذا لم يكن موجوداً
                existing_read = GeneralNotificationRead.query.filter(
                    GeneralNotificationRead.notification_id == notification_id,
                    GeneralNotificationRead.user_id == current_user.id
                ).first()

                if not existing_read:
                    read_record = GeneralNotificationRead(
                        notification_id=notification_id,
                        user_id=current_user.id
                    )
                    db.session.add(read_record)
                    db.session.commit()
        else:
            raise ValueError(f'نوع الإشعار غير مدعوم: {notification_type}')

        # التحقق من نوع الطلب (AJAX أم عادي)
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': True, 'message': 'تم تحديد الإشعار كمقروء'})
        else:
            flash('تم تحديد الإشعار كمقروء', 'success')
    except Exception as e:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'error': str(e)})
        else:
            flash(f'حدث خطأ: {str(e)}', 'danger')

    return redirect(url_for('view_notifications'))

# API endpoint لإرجاع عدد الإشعارات غير المقروءة
@app.route('/api/unread_notifications_count')
@login_required
def api_unread_notifications_count():
    """
    API endpoint لإرجاع عدد الإشعارات غير المقروءة للمستخدم الحالي
    """
    try:
        count = get_unread_notifications_count_for_user(current_user.id)
        return jsonify({'count': count, 'success': True})
    except Exception as e:
        return jsonify({'count': 0, 'success': False, 'error': str(e)})

# لوحة تحكم الأستاذ
@app.route('/dashboard/teacher')
@login_required
def teacher_dashboard():
    try:
        if current_user.role != Role.TEACHER:
            flash('غير مصرح بالوصول', 'danger')
            return redirect(url_for('dashboard'))

        # تحديث سجلات التقدم التي تفتقر إلى material_id
        try:
            update_missing_material_ids(current_user.id)
        except Exception as e:
            print(f"Error updating missing material_ids: {str(e)}")

        # الحصول على جدول الأستاذ وتقدمه
        try:
            schedules = Schedule.query.filter_by(user_id=current_user.id).all()
        except Exception as e:
            print(f"Error fetching schedules: {str(e)}")
            schedules = []

        # الحصول على آخر تحديثات التقدم (جميع التقدمات مع عرض محدود في الواجهة)
        try:
            progress_entries_raw = ProgressEntry.query.filter_by(user_id=current_user.id).order_by(ProgressEntry.date.desc()).all()
        except Exception as e:
            print(f"Error fetching progress entries: {str(e)}")
            progress_entries_raw = []

        # التحقق من وجود الكفاءات المرتبطة
        detailed_entries = []

        # إحصائيات التقدم
        progress_stats = {
            'completed': 0,
            'in_progress': 0,
            'planned': 0,
            'total': 0
        }

        for entry in progress_entries_raw:
            try:
                entry_details = {
                    'id': entry.id,
                    'date': entry.date,
                    'status': entry.status,
                    'notes': entry.notes,
                    'level': None,
                    'subject': None,
                    'domain': None,
                    'material': None,
                    'competency': None
                }

                # تحديث الإحصائيات أولاً
                if entry.status in progress_stats:
                    progress_stats[entry.status] += 1
                progress_stats['total'] += 1

                # التحقق من وجود الكفاءة أولاً (للتقدمات الجديدة والقديمة)
                if entry.competency_id:
                    # محاولة الحصول على الكفاءة من نموذج LevelDataEntry
                    competency = LevelDataEntry.query.filter_by(id=entry.competency_id, entry_type='competency').first()
                    if competency:
                        entry_details['competency'] = {
                            'id': competency.id,
                            'name': competency.name,
                            'description': competency.description
                        }

                        # الحصول على المادة المعرفية (الأب)
                        if competency.parent_id:
                            material = LevelDataEntry.query.filter_by(id=competency.parent_id, entry_type='material').first()
                            if material:
                                entry_details['material'] = {
                                    'id': material.id,
                                    'name': material.name
                                }

                                # الحصول على الميدان (الجد)
                                if material.parent_id:
                                    domain = LevelDataEntry.query.filter_by(id=material.parent_id, entry_type='domain').first()
                                    if domain:
                                        entry_details['domain'] = {
                                            'id': domain.id,
                                            'name': domain.name
                                        }

                                        # الحصول على المادة الدراسية (الجد الأكبر)
                                        if domain.parent_id:
                                            subject = LevelDataEntry.query.filter_by(id=domain.parent_id, entry_type='subject').first()
                                            if subject:
                                                entry_details['subject'] = {
                                                    'id': subject.id,
                                                    'name': subject.name
                                                }

                                                # الحصول على المستوى التعليمي
                                                db = LevelDatabase.query.filter_by(id=subject.database_id).first()
                                                if db and db.level_id:
                                                    level = EducationalLevel.query.get(db.level_id)
                                                    if level:
                                                        entry_details['level'] = {
                                                            'id': level.id,
                                                            'name': level.name
                                                        }
                    else:
                        # محاولة الحصول على الكفاءة من نموذج Competency القديم
                        competency = Competency.query.get(entry.competency_id)
                        if competency:
                            entry_details['competency'] = {
                                'id': competency.id,
                                'name': '',
                                'description': competency.description
                            }

                            # الحصول على المادة المعرفية
                            if competency.knowledge_material_id:
                                material = KnowledgeMaterial.query.get(competency.knowledge_material_id)
                                if material:
                                    entry_details['material'] = {
                                        'id': material.id,
                                        'name': material.name
                                    }

                                    # الحصول على الميدان
                                    if material.domain_id:
                                        domain = Domain.query.get(material.domain_id)
                                        if domain:
                                            entry_details['domain'] = {
                                                'id': domain.id,
                                                'name': domain.name
                                            }

                                            # الحصول على المادة الدراسية
                                            if domain.subject_id:
                                                subject = Subject.query.get(domain.subject_id)
                                                if subject:
                                                    entry_details['subject'] = {
                                                        'id': subject.id,
                                                        'name': subject.name
                                                    }

                                                    # الحصول على المستوى التعليمي
                                                    if subject.level_id:
                                                        level = EducationalLevel.query.get(subject.level_id)
                                                        if level:
                                                            entry_details['level'] = {
                                                                'id': level.id,
                                                                'name': level.name
                                                            }

                # التحقق من وجود المادة المعرفية مباشرة (للتقدمات التي لا تحتوي على competency_id)
                elif entry.material_id:
                    material = LevelDataEntry.query.filter_by(id=entry.material_id, entry_type='material').first()
                    if material:
                        entry_details['material'] = {
                            'id': material.id,
                            'name': material.name
                        }

                        # الحصول على الميدان
                        if material.parent_id:
                            domain = LevelDataEntry.query.filter_by(id=material.parent_id, entry_type='domain').first()
                            if domain:
                                entry_details['domain'] = {
                                    'id': domain.id,
                                    'name': domain.name
                                }

                                # الحصول على المادة الدراسية
                                if domain.parent_id:
                                    subject = LevelDataEntry.query.filter_by(id=domain.parent_id, entry_type='subject').first()
                                    if subject:
                                        entry_details['subject'] = {
                                            'id': subject.id,
                                            'name': subject.name
                                        }

                                        # الحصول على المستوى التعليمي
                                        db = LevelDatabase.query.filter_by(id=subject.database_id).first()
                                        if db and db.level_id:
                                            level = EducationalLevel.query.get(db.level_id)
                                            if level:
                                                entry_details['level'] = {
                                                    'id': level.id,
                                                    'name': level.name
                                                }

                        # إضافة كفاءة وهمية للعرض فقط للتقدمات التي لا تحتوي على competency_id
                        entry_details['competency'] = {
                            'id': None,
                            'name': f"تقدم في {material.name}",
                            'description': f"تقدم في المادة المعرفية: {material.name}"
                        }



                detailed_entries.append(entry_details)
            except Exception as e:
                print(f"Error processing entry {entry.id}: {str(e)}")
                continue

        # حساب نسبة الإنجاز بالطريقة الجديدة (بناءً على المواد المعرفية)
        # للمواد التي يدرسها المعلم فقط
        progress_stats_teacher = calculate_progress_by_materials(current_user.id)

        # حساب إجمالي المواد المعرفية في النظام (جميع المستويات)
        progress_stats_total = calculate_total_system_materials(current_user.id)

        # طباعة تفاصيل للمقارنة
        print(f"=== إحصائيات المعلم {current_user.username} ===")
        print(f"المواد التي يدرسها المعلم: {progress_stats_teacher['total_materials']} مادة معرفية")
        print(f"إجمالي المواد في النظام: {progress_stats_total['total_materials']} مادة معرفية")
        print(f"المواد المكتملة للمعلم: {progress_stats_teacher['completed_materials']}")
        print(f"المواد المكتملة في النظام: {progress_stats_total['completed_materials']}")

        # استخدام نسبة الإنجاز الإجمالية لجميع المواد في النظام
        completion_rate = progress_stats_total['completion_rate']

        # إضافة معلومات المواد المعرفية للإحصائيات
        progress_stats['completed_materials'] = progress_stats_total['completed_materials']
        progress_stats['total_materials'] = progress_stats_total['total_materials']
        progress_stats['completed_by_subject'] = progress_stats_total['completed_by_subject']
        progress_stats['total_by_subject'] = progress_stats_total['total_by_subject']

        # إضافة إحصائيات المعلم المحددة
        progress_stats['teacher_completed_materials'] = progress_stats_teacher['completed_materials']
        progress_stats['teacher_total_materials'] = progress_stats_teacher['total_materials']
        progress_stats['teacher_completion_rate'] = progress_stats_teacher['completion_rate']

        # حساب نسبة الإنجاز القديمة للمقارنة
        old_completion_rate = 0
        if progress_stats['total'] > 0:
            old_completion_rate = (progress_stats['completed'] / progress_stats['total']) * 100

        # الحصول على إحصائيات حسب المستوى والمادة
        level_stats = {}
        subject_stats = {}

        # الحصول على جميع المستويات التي لها تقدمات للمعلم (بدلاً من الاعتماد على الجدول فقط)
        # أولاً: إضافة المستويات من الجدول
        for schedule in schedules:
            # إضافة المستوى إلى الإحصائيات
            if schedule.level_id not in level_stats and schedule.level:
                level_stats[schedule.level_id] = {
                    'name': schedule.level.name,
                    'completed': 0,
                    'in_progress': 0,
                    'planned': 0,
                    'total': 0
                }

            # إضافة المادة إلى الإحصائيات
            if schedule.subject_id not in subject_stats and schedule.subject:
                subject_stats[schedule.subject_id] = {
                    'name': schedule.subject.name,
                    'completed': 0,
                    'in_progress': 0,
                    'planned': 0,
                    'total': 0
                }

        # ثانياً: إضافة المستويات التي لها تقدمات فعلية (حتى لو لم تكن في الجدول)
        progress_levels = ProgressEntry.query.filter_by(user_id=current_user.id).filter(ProgressEntry.level_id.isnot(None)).all()
        for progress in progress_levels:
            if progress.level_id not in level_stats and progress.level_id:
                level = EducationalLevel.query.get(progress.level_id)
                if level:
                    level_stats[progress.level_id] = {
                        'name': level.name,
                        'completed': 0,
                        'in_progress': 0,
                        'planned': 0,
                        'total': 0
                    }

        # تحديث إحصائيات المستوى والمادة بناءً على سجلات التقدم
        for entry_details in detailed_entries:
            try:
                # الحصول على المستوى والمادة من التفاصيل
                if entry_details['level'] and entry_details['level']['id'] in level_stats:
                    level_stats[entry_details['level']['id']][entry_details['status']] += 1
                    level_stats[entry_details['level']['id']]['total'] += 1

                if entry_details['subject'] and entry_details['subject']['id'] in subject_stats:
                    subject_stats[entry_details['subject']['id']][entry_details['status']] += 1
                    subject_stats[entry_details['subject']['id']]['total'] += 1
            except Exception as e:
                print(f"Error updating stats for entry {entry_details['id']}: {str(e)}")
                continue

        # حساب نسب الإنجاز لكل مستوى بالطريقة الجديدة (بناءً على المواد المعرفية)
        for level_id in level_stats:
            # حساب التقدم بناءً على المواد المعرفية للمستوى
            level_progress = calculate_level_progress_by_materials(current_user.id, level_id)
            level_stats[level_id]['completion_rate'] = level_progress['completion_rate']
            level_stats[level_id]['completed_materials'] = level_progress['completed_materials']
            level_stats[level_id]['total_materials'] = level_progress['total_materials']
            level_stats[level_id]['completed_by_subject'] = level_progress['completed_by_subject']
            level_stats[level_id]['total_by_subject'] = level_progress['total_by_subject']



        # حساب نسب الإنجاز لكل مادة بالطريقة القديمة (للمقارنة)
        for subject_id in subject_stats:
            if subject_stats[subject_id]['total'] > 0:
                subject_stats[subject_id]['completion_rate'] = (subject_stats[subject_id]['completed'] / subject_stats[subject_id]['total']) * 100
            else:
                subject_stats[subject_id]['completion_rate'] = 0

        # الحصول على آخر 3 إشعارات فقط
        try:
            notifications = InspectorTeacherNotification.query.filter_by(receiver_id=current_user.id).order_by(InspectorTeacherNotification.created_at.desc()).limit(3).all()
        except Exception as e:
            print(f"Error fetching notifications: {str(e)}")
            notifications = []

        # الحصول على جميع المستويات والمواد المتاحة في النظام
        all_levels = EducationalLevel.query.all()
        all_subjects = LevelDataEntry.query.filter_by(entry_type='subject').all()

        # إنشاء قواميس لجميع المستويات والمواد
        all_level_stats = {}
        for level in all_levels:
            all_level_stats[level.id] = {
                'name': level.name,
                'completed': 0,
                'in_progress': 0,
                'planned': 0,
                'total': 0,
                'completion_rate': 0
            }

        all_subject_stats = {}
        for subject in all_subjects:
            # الحصول على معرف المستوى من قاعدة البيانات
            level_id = None
            if subject.database_id:
                db = LevelDatabase.query.get(subject.database_id)
                if db and db.level_id:
                    level_id = db.level_id

            all_subject_stats[subject.id] = {
                'name': subject.name,
                'level_id': level_id,
                'completed': 0,
                'in_progress': 0,
                'planned': 0,
                'total': 0,
                'completion_rate': 0
            }

        # دمج الإحصائيات الحالية مع الإحصائيات الكاملة
        for level_id, stats in level_stats.items():
            if level_id in all_level_stats:
                all_level_stats[level_id].update(stats)

        for subject_id, stats in subject_stats.items():
            if subject_id in all_subject_stats:
                all_subject_stats[subject_id].update(stats)

        # التأكد من وجود المتغيرات المطلوبة
        context = {
            'schedules': schedules or [],
            'progress_entries': detailed_entries or [],
            'progress_stats': progress_stats,
            'completion_rate': completion_rate,
            'level_stats': all_level_stats,
            'subject_stats': all_subject_stats,
            'notifications': notifications
        }

        return render_template('teacher_dashboard.html', **context)

    except Exception as e:
        print(f"Error in teacher_dashboard: {str(e)}")
        flash('حدث خطأ أثناء تحميل لوحة التحكم', 'danger')

        # تمرير قيم افتراضية لجميع المتغيرات المطلوبة
        default_context = {
            'schedules': [],
            'progress_entries': [],
            'progress_stats': {
                'completed': 0,
                'in_progress': 0,
                'planned': 0,
                'total': 0,
                'completed_materials': 0,
                'total_materials': 0,
                'completed_by_subject': {},
                'total_by_subject': {}
            },
            'completion_rate': 0,
            'level_stats': {},
            'subject_stats': {},
            'notifications': []
        }

        return render_template('teacher_dashboard.html', **default_context)

# البرنامج السنوي للتدريس
@app.route('/teaching-program')
@login_required
def teaching_program():
    levels = EducationalLevel.query.all()

    # الحصول على قواعد البيانات النشطة لكل مستوى
    level_databases = {}
    for level in levels:
        db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
        if db:
            level_databases[level.id] = db.id

    return render_template('teaching_program.html', levels=levels, level_databases=level_databases)

@app.route('/admin/cleanup-inactive-levels', methods=['GET'])
@login_required
def cleanup_inactive_levels():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # الحصول على جميع المستويات التعليمية
        all_levels = EducationalLevel.query.all()
        deleted_count = 0

        for level in all_levels:
            # التحقق من وجود قاعدة بيانات نشطة لهذا المستوى
            active_db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()

            if not active_db:
                # حذف المستوى إذا لم يكن لديه قاعدة بيانات نشطة
                db.session.delete(level)
                deleted_count += 1

        # حفظ التغييرات
        db.session.commit()

        if deleted_count > 0:
            flash(f'تم حذف {deleted_count} مستوى تعليمي ليس لديه قاعدة بيانات نشطة', 'success')
        else:
            flash('لم يتم العثور على مستويات تعليمية ليس لديها قواعد بيانات نشطة', 'info')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المستويات: {str(e)}', 'danger')

    return redirect(url_for('teaching_program'))

# مسارات API للقوائم المنسدلة المعتمدة
@app.route('/api/subjects/<int:level_id>')
def get_subjects(level_id):
    result_subjects = []

    # الحصول على المواد من قاعدة البيانات الخاصة بالمستوى فقط
    level_db = LevelDatabase.query.filter_by(level_id=level_id, is_active=True).first()
    if level_db:
        db_subjects = LevelDataEntry.query.filter_by(
            database_id=level_db.id,
            entry_type='subject',
            is_active=True
        ).order_by(LevelDataEntry.order_num, LevelDataEntry.name).all()

        # إضافة المواد من قاعدة البيانات
        for subject in db_subjects:
            result_subjects.append({
                'id': subject.id,
                'name': subject.name,
                'source': 'database'
            })

    return jsonify(result_subjects)

@app.route('/api/domains/<int:subject_id>')
def get_domains(subject_id):
    # الحصول على المادة الدراسية
    subject = LevelDataEntry.query.get(subject_id)
    if not subject:
        return jsonify([])

    # الحصول على الميادين المرتبطة بالمادة الدراسية
    domains = LevelDataEntry.query.filter_by(database_id=subject.database_id, entry_type='domain', parent_id=subject_id, is_active=True).all()

    # إزالة التكرارات
    unique_domains = []
    seen_ids = set()

    for domain in domains:
        if domain.id not in seen_ids:
            seen_ids.add(domain.id)
            unique_domains.append(domain)

    return jsonify([{'id': d.id, 'name': d.name} for d in unique_domains])

@app.route('/api/knowledge-materials/<int:domain_id>')
def get_knowledge_materials(domain_id):
    # الحصول على الميدان
    domain = LevelDataEntry.query.get(domain_id)
    if not domain:
        return jsonify([])

    # الحصول على المواد المعرفية المرتبطة بالميدان
    materials = LevelDataEntry.query.filter_by(database_id=domain.database_id, entry_type='material', parent_id=domain_id, is_active=True).all()

    # إزالة التكرارات
    unique_materials = []
    seen_ids = set()

    for material in materials:
        if material.id not in seen_ids:
            seen_ids.add(material.id)
            unique_materials.append(material)

    return jsonify([{'id': m.id, 'name': m.name} for m in unique_materials])

@app.route('/api/competencies/<int:material_id>')
def get_competencies(material_id):
    # الحصول على المادة المعرفية
    material = LevelDataEntry.query.get(material_id)
    if not material:
        return jsonify([])

    # الحصول على الكفاءات المرتبطة بالمادة المعرفية
    competencies = LevelDataEntry.query.filter_by(database_id=material.database_id, entry_type='competency', parent_id=material_id, is_active=True).all()

    # إزالة التكرارات
    unique_competencies = []
    seen_ids = set()

    for competency in competencies:
        if competency.id not in seen_ids:
            seen_ids.add(competency.id)
            unique_competencies.append(competency)

    return jsonify([{
        'id': c.id,
        'name': c.name or '',
        'description': c.description or ''
    } for c in unique_competencies])

# تسجيل التقدم
@app.route('/progress/add', methods=['POST'])
@login_required
def add_progress():
    competency_id = request.form.get('competency_id')
    date_str = request.form.get('date')
    status = request.form.get('status')
    notes = request.form.get('notes')

    # التحقق من وجود الكفاءة
    competency = LevelDataEntry.query.get(competency_id)
    if not competency or competency.entry_type != 'competency':
        flash('الكفاءة غير موجودة', 'danger')
        return redirect(url_for('teaching_program'))

    # تحويل التاريخ من نص إلى كائن date
    try:
        date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        flash('تنسيق التاريخ غير صحيح', 'danger')
        return redirect(url_for('teaching_program'))

    # التحقق من الحالة
    if status not in ['completed', 'in_progress', 'planned']:
        status = 'planned'

    # استنتاج معلومات التسلسل الهرمي من الكفاءة
    material_id = None
    domain_id = None
    subject_id = None
    level_id = None

    # البحث عن المادة المعرفية (parent الكفاءة)
    if competency.parent_id:
        material = LevelDataEntry.query.filter_by(
            id=competency.parent_id,
            entry_type='material'
        ).first()

        if material:
            material_id = material.id

            # البحث عن الميدان (parent المادة المعرفية)
            if material.parent_id:
                domain = LevelDataEntry.query.filter_by(
                    id=material.parent_id,
                    entry_type='domain'
                ).first()

                if domain:
                    domain_id = domain.id

                    # البحث عن المادة الدراسية (parent الميدان)
                    if domain.parent_id:
                        subject = LevelDataEntry.query.filter_by(
                            id=domain.parent_id,
                            entry_type='subject'
                        ).first()

                        if subject:
                            subject_id = subject.id

                            # البحث عن المستوى من قاعدة البيانات
                            level_db = LevelDatabase.query.filter_by(
                                id=subject.database_id,
                                is_active=True
                            ).first()

                            if level_db:
                                level_id = level_db.level_id

    # إنشاء سجل تقدم جديد مع جميع المعلومات
    new_progress = ProgressEntry(
        user_id=current_user.id,
        competency_id=competency_id,
        material_id=material_id,
        domain_id=domain_id,
        subject_id=subject_id,
        level_id=level_id,
        date=date,
        status=status,
        notes=notes
    )

    db.session.add(new_progress)

    try:
        db.session.commit()
        flash('تم إضافة التقدم بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"خطأ في حفظ التقدم: {str(e)}")
        flash('حدث خطأ أثناء حفظ التقدم', 'danger')

    return redirect(url_for('teaching_program'))

# إدارة جدول التدريس
@app.route('/schedule/manage')
@login_required
def manage_schedule():
    if current_user.role != Role.TEACHER:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    schedules = Schedule.query.filter_by(user_id=current_user.id).all()
    levels = EducationalLevel.query.all()

    return render_template('manage_schedule.html', schedules=schedules, levels=levels)

@app.route('/schedule/add', methods=['POST'])
@login_required
def add_schedule():
    day_of_week = int(request.form.get('day_of_week'))
    start_time_str = request.form.get('start_time')
    end_time_str = request.form.get('end_time')
    subject_id = request.form.get('subject_id')
    level_id = request.form.get('level_id')

    # تحويل الوقت من نص إلى كائن time
    start_time = datetime.strptime(start_time_str, '%H:%M').time()
    end_time = datetime.strptime(end_time_str, '%H:%M').time()

    # التحقق من وجود المادة الدراسية
    subject = None

    # محاولة الحصول على المادة من جدول Subject
    subject = Subject.query.filter_by(id=subject_id).first()

    # إذا لم يتم العثور على المادة، نحاول البحث في قاعدة البيانات الخاصة بالمستوى
    if not subject:
        # الحصول على قاعدة البيانات النشطة للمستوى
        level_db = LevelDatabase.query.filter_by(level_id=level_id, is_active=True).first()
        if level_db:
            # الحصول على المادة من قاعدة البيانات
            entry = LevelDataEntry.query.filter_by(id=subject_id, database_id=level_db.id, entry_type='subject').first()
            if entry:
                # البحث عن مادة موجودة بنفس الاسم في جدول Subject
                subject = Subject.query.filter_by(name=entry.name, level_id=level_id).first()

                # إنشاء مادة جديدة إذا لم تكن موجودة
                if not subject:
                    subject = Subject(name=entry.name, level_id=level_id)
                    db.session.add(subject)
                    db.session.flush()  # للحصول على معرف المادة الجديدة

    if not subject:
        flash('لم يتم العثور على المادة الدراسية', 'danger')
        return redirect(url_for('manage_schedule'))

    new_schedule = Schedule(
        user_id=current_user.id,
        day_of_week=day_of_week,
        start_time=start_time,
        end_time=end_time,
        subject_id=subject.id,
        level_id=level_id
    )

    db.session.add(new_schedule)
    db.session.commit()

    flash('تم إضافة الحصة بنجاح', 'success')
    return redirect(url_for('manage_schedule'))

@app.route('/schedule/delete/<int:schedule_id>', methods=['POST'])
@login_required
def delete_schedule(schedule_id):
    schedule = Schedule.query.get_or_404(schedule_id)

    # التحقق من أن الجدول ينتمي للمستخدم الحالي
    if schedule.user_id != current_user.id:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('manage_schedule'))

    db.session.delete(schedule)
    db.session.commit()

    flash('تم حذف الحصة بنجاح', 'success')
    return redirect(url_for('manage_schedule'))

@app.route('/api/schedule/<int:schedule_id>')
@login_required
def get_schedule(schedule_id):
    schedule = Schedule.query.get_or_404(schedule_id)

    # التحقق من أن الجدول ينتمي للمستخدم الحالي
    if schedule.user_id != current_user.id:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403

    # تحويل الوقت إلى تنسيق مناسب لحقل الإدخال من نوع time
    start_time = schedule.start_time.strftime('%H:%M')
    end_time = schedule.end_time.strftime('%H:%M')

    return jsonify({
        'id': schedule.id,
        'day_of_week': schedule.day_of_week,
        'start_time': start_time,
        'end_time': end_time,
        'subject_id': schedule.subject_id,
        'level_id': schedule.level_id
    })

@app.route('/schedule/edit/<int:schedule_id>', methods=['POST'])
@login_required
def edit_schedule(schedule_id):
    schedule = Schedule.query.get_or_404(schedule_id)

    # التحقق من أن الجدول ينتمي للمستخدم الحالي
    if schedule.user_id != current_user.id:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('manage_schedule'))

    # الحصول على البيانات من النموذج
    day_of_week = int(request.form.get('day_of_week'))
    start_time_str = request.form.get('start_time')
    end_time_str = request.form.get('end_time')
    subject_id = request.form.get('subject_id')
    level_id = request.form.get('level_id')

    # تحويل الوقت من نص إلى كائن time
    start_time = datetime.strptime(start_time_str, '%H:%M').time()
    end_time = datetime.strptime(end_time_str, '%H:%M').time()

    # التحقق من وجود المادة الدراسية
    subject = None

    # محاولة الحصول على المادة من جدول Subject
    subject = Subject.query.filter_by(id=subject_id).first()

    # إذا لم يتم العثور على المادة، نحاول البحث في قاعدة البيانات الخاصة بالمستوى
    if not subject:
        # الحصول على قاعدة البيانات النشطة للمستوى
        level_db = LevelDatabase.query.filter_by(level_id=level_id, is_active=True).first()
        if level_db:
            # الحصول على المادة من قاعدة البيانات
            entry = LevelDataEntry.query.filter_by(id=subject_id, database_id=level_db.id, entry_type='subject').first()
            if entry:
                # البحث عن مادة موجودة بنفس الاسم في جدول Subject
                subject = Subject.query.filter_by(name=entry.name, level_id=level_id).first()

                # إنشاء مادة جديدة إذا لم تكن موجودة
                if not subject:
                    subject = Subject(name=entry.name, level_id=level_id)
                    db.session.add(subject)
                    db.session.flush()  # للحصول على معرف المادة الجديدة

    if not subject:
        flash('لم يتم العثور على المادة الدراسية', 'danger')
        return redirect(url_for('manage_schedule'))

    # تحديث بيانات الحصة
    schedule.day_of_week = day_of_week
    schedule.start_time = start_time
    schedule.end_time = end_time
    schedule.subject_id = subject.id
    schedule.level_id = level_id

    db.session.commit()

    flash('تم تحديث الحصة بنجاح', 'success')
    return redirect(url_for('manage_schedule'))

# تصدير واستيراد البيانات
@app.route('/data/export/<string:model_name>')
@login_required
def export_data(model_name):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    model_map = {
        'levels': EducationalLevel,
        'subjects': Subject,
        'domains': Domain,
        'materials': KnowledgeMaterial,
        'competencies': Competency
    }

    if model_name not in model_map:
        flash('اسم النموذج غير صحيح', 'danger')
        return redirect(url_for('admin_dashboard'))

    model = model_map[model_name]
    data = model.query.all()

    # تحويل إلى DataFrame
    df_data = []
    for item in data:
        item_dict = {column.name: getattr(item, column.name) for column in item.__table__.columns}
        df_data.append(item_dict)

    df = pd.DataFrame(df_data)

    # حفظ إلى Excel
    filename = f"{model_name}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    filepath = os.path.join(app.root_path, 'static', 'exports', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    df.to_excel(filepath, index=False)

    return redirect(url_for('static', filename=f'exports/{filename}'))

@app.route('/data/import/<string:model_name>', methods=['POST'])
@login_required
def import_data(model_name):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    model_map = {
        'levels': EducationalLevel,
        'subjects': Subject,
        'domains': Domain,
        'materials': KnowledgeMaterial,
        'competencies': Competency
    }

    if model_name not in model_map:
        flash('اسم النموذج غير صحيح', 'danger')
        return redirect(url_for('admin_dashboard'))

    if 'file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('admin_dashboard'))

    file = request.files['file']

    if file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('admin_dashboard'))

    if not file.filename.endswith('.xlsx'):
        flash('يجب أن يكون الملف بصيغة Excel (.xlsx)', 'danger')
        return redirect(url_for('admin_dashboard'))

    # قراءة ملف Excel
    df = pd.read_excel(file)

    # استيراد البيانات
    model = model_map[model_name]
    for _, row in df.iterrows():
        # تحويل الصف إلى قاموس
        row_dict = row.to_dict()

        # إزالة عمود id إذا كان موجوداً
        if 'id' in row_dict:
            del row_dict['id']

        # إنشاء نموذج جديد
        new_instance = model(**row_dict)
        db.session.add(new_instance)

    db.session.commit()

    flash(f'تم استيراد البيانات بنجاح لـ {model_name}', 'success')
    return redirect(url_for('admin_dashboard'))

# تصدير جميع قواعد البيانات
@app.route('/admin/databases/export-all')
@login_required
def export_all_databases():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    # إنشاء كاتب لملف Excel
    filename = f"all_databases_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    filepath = os.path.join(app.root_path, 'static', 'exports', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    # التحقق من وجود بيانات للتصدير
    levels = EducationalLevel.query.all()
    databases = LevelDatabase.query.all()

    if not levels and not databases:
        flash('لا توجد بيانات للتصدير', 'warning')
        return redirect(url_for('manage_databases'))

    # إنشاء كاتب Excel
    writer = pd.ExcelWriter(filepath, engine='openpyxl')
    has_data = False

    # تصدير المستويات
    if levels:
        levels_data = [{
            'id': level.id,
            'name': level.name,
            'is_active': level.is_active,
            'database_prefix': level.database_prefix
        } for level in levels]

        pd.DataFrame(levels_data).to_excel(writer, sheet_name='Levels', index=False)
        has_data = True

    # تصدير قواعد البيانات
    if databases:
        databases_data = [{
            'id': db.id,
            'level_id': db.level_id,
            'level_name': db.level.name if db.level else '',
            'name': db.name,
            'file_path': db.file_path,
            'is_active': db.is_active,
            'created_at': db.created_at.strftime('%Y-%m-%d %H:%M:%S')
        } for db in databases]

        pd.DataFrame(databases_data).to_excel(writer, sheet_name='Databases', index=False)
        has_data = True

    # تصدير بيانات كل قاعدة بيانات
    for db in databases:
        if db.is_active:
            # تصدير المواد الدراسية
            subjects = LevelDataEntry.query.filter_by(database_id=db.id, entry_type='subject').all()
            if subjects:
                subjects_data = [{
                    'id': s.id,
                    'database_id': s.database_id,
                    'database_name': db.name,
                    'name': s.name,
                    'description': s.description,
                    'is_active': s.is_active
                } for s in subjects]

                sheet_name = f"Subjects_{db.id}"
                # التأكد من أن اسم الورقة لا يتجاوز 31 حرفًا (حد Excel)
                if len(sheet_name) > 31:
                    sheet_name = sheet_name[:31]
                pd.DataFrame(subjects_data).to_excel(writer, sheet_name=sheet_name, index=False)
                has_data = True

    # إذا لم تكن هناك أي بيانات للتصدير، أضف ورقة فارغة
    if not has_data:
        pd.DataFrame([{'info': 'لا توجد بيانات'}]).to_excel(writer, sheet_name='Info', index=False)

    writer.close()

    return redirect(url_for('static', filename=f'exports/{filename}'))

# تصدير نوع محدد من بيانات قاعدة البيانات
@app.route('/admin/databases/<int:db_id>/export/<string:entry_type>')
@login_required
def export_database_entry_type(db_id, entry_type):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    # التحقق من نوع البيانات
    valid_types = ['subject', 'domain', 'material', 'competency']
    if entry_type not in valid_types:
        flash('نوع البيانات غير صحيح', 'danger')
        return redirect(url_for('view_database', db_id=db_id))

    # إنشاء كاتب لملف Excel
    type_name = {
        'subject': 'المواد_الدراسية',
        'domain': 'الميادين',
        'material': 'المواد_المعرفية',
        'competency': 'الكفاءات'
    }

    filename = f"{database.name}_{type_name[entry_type]}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    filepath = os.path.join(app.root_path, 'static', 'exports', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    writer = pd.ExcelWriter(filepath, engine='openpyxl')

    # تصدير معلومات قاعدة البيانات
    db_info = [{
        'id': database.id,
        'name': database.name,
        'level': database.level.name if database.level else '',
        'level_id': database.level_id,
        'file_path': database.file_path,
        'is_active': database.is_active,
        'created_at': database.created_at.strftime('%Y-%m-%d %H:%M:%S')
    }]
    pd.DataFrame(db_info).to_excel(writer, sheet_name='Database_Info', index=False)

    # تصدير البيانات حسب النوع
    entries = LevelDataEntry.query.filter_by(database_id=db_id, entry_type=entry_type).all()

    if entries:
        if entry_type == 'subject':
            entries_data = [{
                'id': e.id,
                'name': e.name,
                'description': e.description,
                'is_active': e.is_active,
                'database_id': db_id,
                'level_id': database.level_id,
                'level_name': database.level.name if database.level else ''
            } for e in entries]
        elif entry_type == 'domain':
            entries_data = []
            for e in entries:
                parent = LevelDataEntry.query.get(e.parent_id) if e.parent_id else None
                entries_data.append({
                    'id': e.id,
                    'name': e.name,
                    'description': e.description,
                    'parent_id': e.parent_id,
                    'parent_name': parent.name if parent else '',
                    'subject_id': e.parent_id,  # معرف المادة الدراسية
                    'subject_name': parent.name if parent else '',
                    'database_id': db_id,
                    'level_id': database.level_id,
                    'level_name': database.level.name if database.level else '',
                    'is_active': e.is_active
                })
        elif entry_type == 'material':
            entries_data = []
            for e in entries:
                parent = LevelDataEntry.query.get(e.parent_id) if e.parent_id else None
                subject_id = None
                subject_name = ''

                # الحصول على المادة الدراسية المرتبطة بالميدان
                if parent and parent.parent_id:
                    subject = LevelDataEntry.query.get(parent.parent_id)
                    if subject:
                        subject_id = subject.id
                        subject_name = subject.name

                entries_data.append({
                    'id': e.id,
                    'name': e.name,
                    'description': e.description,
                    'parent_id': e.parent_id,  # معرف الميدان
                    'parent_name': parent.name if parent else '',  # اسم الميدان
                    'domain_id': e.parent_id,
                    'domain_name': parent.name if parent else '',
                    'subject_id': subject_id,
                    'subject_name': subject_name,
                    'database_id': db_id,
                    'level_id': database.level_id,
                    'level_name': database.level.name if database.level else '',
                    'is_active': e.is_active
                })
        elif entry_type == 'competency':
            entries_data = []
            for e in entries:
                parent = LevelDataEntry.query.get(e.parent_id) if e.parent_id else None
                domain_id = None
                domain_name = ''
                subject_id = None
                subject_name = ''

                # الحصول على الميدان والمادة الدراسية المرتبطة بالمادة المعرفية
                if parent and parent.parent_id:
                    domain = LevelDataEntry.query.get(parent.parent_id)
                    if domain:
                        domain_id = domain.id
                        domain_name = domain.name
                        if domain.parent_id:
                            subject = LevelDataEntry.query.get(domain.parent_id)
                            if subject:
                                subject_id = subject.id
                                subject_name = subject.name

                entries_data.append({
                    'id': e.id,
                    'name': e.name,
                    'description': e.description,
                    'parent_id': e.parent_id,  # معرف المادة المعرفية
                    'parent_name': parent.name if parent else '',  # اسم المادة المعرفية
                    'material_id': e.parent_id,
                    'material_name': parent.name if parent else '',
                    'domain_id': domain_id,
                    'domain_name': domain_name,
                    'subject_id': subject_id,
                    'subject_name': subject_name,
                    'database_id': db_id,
                    'level_id': database.level_id,
                    'level_name': database.level.name if database.level else '',
                    'is_active': e.is_active
                })

        # إضافة ورقة للبيانات المترابطة
        if entry_type == 'competency':
            hierarchical_data = []
            for e in entries:
                parent_material = LevelDataEntry.query.get(e.parent_id) if e.parent_id else None
                domain_id = None
                domain_name = ''
                subject_id = None
                subject_name = ''

                if parent_material and parent_material.parent_id:
                    domain = LevelDataEntry.query.get(parent_material.parent_id)
                    if domain:
                        domain_id = domain.id
                        domain_name = domain.name
                        if domain.parent_id:
                            subject = LevelDataEntry.query.get(domain.parent_id)
                            if subject:
                                subject_id = subject.id
                                subject_name = subject.name

                hierarchical_data.append({
                    'level_id': database.level_id,
                    'level_name': database.level.name if database.level else '',
                    'subject_id': subject_id,
                    'subject_name': subject_name,
                    'domain_id': domain_id,
                    'domain_name': domain_name,
                    'material_id': e.parent_id,
                    'material_name': parent_material.name if parent_material else '',
                    'competency_id': e.id,
                    'competency_name': e.name,
                    'competency_description': e.description,
                    'is_active': e.is_active
                })

            if hierarchical_data:
                pd.DataFrame(hierarchical_data).to_excel(writer, sheet_name='Hierarchical_Data', index=False)

        sheet_name = type_name[entry_type]
        if len(sheet_name) > 31:
            sheet_name = sheet_name[:31]
        pd.DataFrame(entries_data).to_excel(writer, sheet_name=sheet_name, index=False)
    else:
        pd.DataFrame([{'info': f'لا توجد بيانات من نوع {type_name[entry_type]}'}]).to_excel(writer, sheet_name='Info', index=False)

    writer.close()

    return redirect(url_for('static', filename=f'exports/{filename}'))

# تصدير واستيراد بيانات قاعدة البيانات
@app.route('/admin/databases/<int:db_id>/export')
@login_required
def export_database_data(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    # إنشاء كاتب لملف Excel
    filename = f"database_{database.name}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    filepath = os.path.join(app.root_path, 'static', 'exports', filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    writer = pd.ExcelWriter(filepath, engine='openpyxl')
    has_data = False

    # تصدير معلومات قاعدة البيانات
    db_info = [{
        'id': database.id,
        'name': database.name,
        'level': database.level.name if database.level else '',
        'level_id': database.level_id,
        'file_path': database.file_path,
        'is_active': database.is_active,
        'created_at': database.created_at.strftime('%Y-%m-%d %H:%M:%S')
    }]
    pd.DataFrame(db_info).to_excel(writer, sheet_name='Database_Info', index=False)
    has_data = True

    # تصدير المواد الدراسية
    subjects = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='subject').all()
    subjects_data = [{
        'id': s.id,
        'name': s.name,
        'description': s.description,
        'is_active': s.is_active,
        'database_id': db_id,
        'level_id': database.level_id,
        'level_name': database.level.name if database.level else ''
    } for s in subjects]

    if subjects_data:
        pd.DataFrame(subjects_data).to_excel(writer, sheet_name='Subjects', index=False)
        has_data = True

    # تصدير الميادين
    domains = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='domain').all()
    domains_data = []

    for d in domains:
        parent = LevelDataEntry.query.get(d.parent_id) if d.parent_id else None
        domains_data.append({
            'id': d.id,
            'name': d.name,
            'description': d.description,
            'parent_id': d.parent_id,
            'parent_name': parent.name if parent else '',
            'subject_id': d.parent_id,  # معرف المادة الدراسية
            'subject_name': parent.name if parent else '',
            'database_id': db_id,
            'level_id': database.level_id,
            'level_name': database.level.name if database.level else '',
            'is_active': d.is_active
        })

    if domains_data:
        pd.DataFrame(domains_data).to_excel(writer, sheet_name='Domains', index=False)
        has_data = True

    # تصدير المواد المعرفية
    materials = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='material').all()
    materials_data = []

    for m in materials:
        parent = LevelDataEntry.query.get(m.parent_id) if m.parent_id else None
        subject_id = None
        subject_name = ''

        # الحصول على المادة الدراسية المرتبطة بالميدان
        if parent and parent.parent_id:
            subject = LevelDataEntry.query.get(parent.parent_id)
            if subject:
                subject_id = subject.id
                subject_name = subject.name

        materials_data.append({
            'id': m.id,
            'name': m.name,
            'description': m.description,
            'parent_id': m.parent_id,  # معرف الميدان
            'parent_name': parent.name if parent else '',  # اسم الميدان
            'domain_id': m.parent_id,
            'domain_name': parent.name if parent else '',
            'subject_id': subject_id,
            'subject_name': subject_name,
            'database_id': db_id,
            'level_id': database.level_id,
            'level_name': database.level.name if database.level else '',
            'is_active': m.is_active
        })

    if materials_data:
        pd.DataFrame(materials_data).to_excel(writer, sheet_name='Materials', index=False)
        has_data = True

    # تصدير الكفاءات
    competencies = LevelDataEntry.query.filter_by(database_id=db_id, entry_type='competency').all()
    competencies_data = []

    for c in competencies:
        parent = LevelDataEntry.query.get(c.parent_id) if c.parent_id else None
        domain_id = None
        domain_name = ''
        subject_id = None
        subject_name = ''

        # الحصول على الميدان والمادة الدراسية المرتبطة بالمادة المعرفية
        if parent and parent.parent_id:
            domain = LevelDataEntry.query.get(parent.parent_id)
            if domain:
                domain_id = domain.id
                domain_name = domain.name
                if domain.parent_id:
                    subject = LevelDataEntry.query.get(domain.parent_id)
                    if subject:
                        subject_id = subject.id
                        subject_name = subject.name

        competencies_data.append({
            'id': c.id,
            'name': c.name,
            'description': c.description,
            'parent_id': c.parent_id,  # معرف المادة المعرفية
            'parent_name': parent.name if parent else '',  # اسم المادة المعرفية
            'material_id': c.parent_id,
            'material_name': parent.name if parent else '',
            'domain_id': domain_id,
            'domain_name': domain_name,
            'subject_id': subject_id,
            'subject_name': subject_name,
            'database_id': db_id,
            'level_id': database.level_id,
            'level_name': database.level.name if database.level else '',
            'is_active': c.is_active
        })

    if competencies_data:
        pd.DataFrame(competencies_data).to_excel(writer, sheet_name='Competencies', index=False)
        has_data = True

    # تصدير البيانات المترابطة في ورقة واحدة
    hierarchical_data = []

    # إضافة الكفاءات مع جميع العلاقات
    for c in competencies:
        parent_material = LevelDataEntry.query.get(c.parent_id) if c.parent_id else None
        domain_id = None
        domain_name = ''
        subject_id = None
        subject_name = ''

        if parent_material and parent_material.parent_id:
            domain = LevelDataEntry.query.get(parent_material.parent_id)
            if domain:
                domain_id = domain.id
                domain_name = domain.name
                if domain.parent_id:
                    subject = LevelDataEntry.query.get(domain.parent_id)
                    if subject:
                        subject_id = subject.id
                        subject_name = subject.name

        hierarchical_data.append({
            'level_id': database.level_id,
            'level_name': database.level.name if database.level else '',
            'subject_id': subject_id,
            'subject_name': subject_name,
            'domain_id': domain_id,
            'domain_name': domain_name,
            'material_id': c.parent_id,
            'material_name': parent_material.name if parent_material else '',
            'competency_id': c.id,
            'competency_name': c.name,
            'competency_description': c.description,
            'is_active': c.is_active
        })

    if hierarchical_data:
        pd.DataFrame(hierarchical_data).to_excel(writer, sheet_name='Hierarchical_Data', index=False)
        has_data = True

    # إذا لم تكن هناك أي بيانات للتصدير بخلاف معلومات قاعدة البيانات
    if not has_data:
        pd.DataFrame([{'info': 'لا توجد بيانات في قاعدة البيانات'}]).to_excel(writer, sheet_name='Info', index=False)

    writer.close()

    return redirect(url_for('static', filename=f'exports/{filename}'))

@app.route('/admin/import-all-levels-page', methods=['GET'])
@login_required
def import_all_levels_page():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('import_all_levels.html')

@app.route('/admin/generate-primary-database', methods=['GET'])
@login_required
def generate_primary_database():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # إنشاء المستويات الخمسة للتعليم الابتدائي
        primary_levels = [
            'السنة الأولى ابتدائي',
            'السنة الثانية ابتدائي',
            'السنة الثالثة ابتدائي',
            'السنة الرابعة ابتدائي',
            'السنة الخامسة ابتدائي'
        ]

        # إنشاء المواد الدراسية الأساسية
        subjects = [
            'اللغة العربية',
            'الرياضيات',
            'التربية العلمية والتكنولوجية',
            'التربية الإسلامية',
            'التربية المدنية',
            'التاريخ والجغرافيا',
            'اللغة الفرنسية',
            'التربية الفنية',
            'التربية البدنية'
        ]

        # إنشاء الميادين لكل مادة
        domains = {
            'اللغة العربية': [
                'فهم المنطوق',
                'التعبير الشفهي',
                'فهم المكتوب',
                'التعبير الكتابي'
            ],
            'الرياضيات': [
                'الأعداد والحساب',
                'الفضاء والهندسة',
                'القياس',
                'تنظيم المعطيات'
            ],
            'التربية العلمية والتكنولوجية': [
                'الإنسان والصحة',
                'الظواهر الطبيعية',
                'المادة وعالم الأشياء'
            ]
        }

        # إنشاء المستويات التعليمية في قاعدة البيانات
        for level_name in primary_levels:
            # التحقق من وجود المستوى
            level = EducationalLevel.query.filter_by(name=level_name).first()
            if not level:
                level = EducationalLevel(name=level_name)
                db.session.add(level)
                db.session.flush()  # للحصول على معرف المستوى

            # التحقق من وجود قاعدة بيانات نشطة لهذا المستوى
            database = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
            if not database:
                # إنشاء قاعدة بيانات جديدة للمستوى
                db_name = f"{level_name}_DB"
                database = LevelDatabase(
                    name=db_name,
                    level_id=level.id,
                    file_path=f"databases/{db_name}.db",
                    is_active=True
                )
                db.session.add(database)
                db.session.flush()  # للحصول على معرف قاعدة البيانات

            # إنشاء المواد الدراسية لهذا المستوى
            for subject_name in subjects:
                # التحقق من وجود المادة الدراسية
                subject = LevelDataEntry.query.filter_by(
                    database_id=database.id,
                    entry_type='subject',
                    name=subject_name
                ).first()

                if not subject:
                    subject = LevelDataEntry(
                        database_id=database.id,
                        entry_type='subject',
                        name=subject_name,
                        is_active=True
                    )
                    db.session.add(subject)
                    db.session.flush()

                # إنشاء الميادين لهذه المادة
                if subject_name in domains:
                    for domain_name in domains[subject_name]:
                        # التحقق من وجود الميدان
                        domain = LevelDataEntry.query.filter_by(
                            database_id=database.id,
                            entry_type='domain',
                            name=domain_name,
                            parent_id=subject.id
                        ).first()

                        if not domain:
                            domain = LevelDataEntry(
                                database_id=database.id,
                                entry_type='domain',
                                name=domain_name,
                                parent_id=subject.id,
                                is_active=True
                            )
                            db.session.add(domain)
                            db.session.flush()

                        # إنشاء مواد معرفية افتراضية لكل ميدان
                        for i in range(1, 4):  # إنشاء 3 مواد معرفية لكل ميدان
                            material_name = f"{domain_name} - المادة المعرفية {i}"

                            # التحقق من وجود المادة المعرفية
                            material = LevelDataEntry.query.filter_by(
                                database_id=database.id,
                                entry_type='material',
                                name=material_name,
                                parent_id=domain.id
                            ).first()

                            if not material:
                                material = LevelDataEntry(
                                    database_id=database.id,
                                    entry_type='material',
                                    name=material_name,
                                    parent_id=domain.id,
                                    is_active=True
                                )
                                db.session.add(material)
                                db.session.flush()

                            # إنشاء كفاءات مستهدفة لكل مادة معرفية
                            for j in range(1, 3):  # إنشاء كفاءتين لكل مادة معرفية
                                competency_name = f"الكفاءة {j} لـ {material_name}"
                                competency_description = f"وصف الكفاءة {j} لـ {material_name}"

                                # التحقق من وجود الكفاءة
                                competency = LevelDataEntry.query.filter_by(
                                    database_id=database.id,
                                    entry_type='competency',
                                    name=competency_name,
                                    parent_id=material.id
                                ).first()

                                if not competency:
                                    competency = LevelDataEntry(
                                        database_id=database.id,
                                        entry_type='competency',
                                        name=competency_name,
                                        description=competency_description,
                                        parent_id=material.id,
                                        is_active=True
                                    )
                                    db.session.add(competency)

        # حفظ التغييرات
        db.session.commit()
        flash('تم إنشاء قواعد البيانات للمستويات الخمسة للتعليم الابتدائي بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إنشاء قواعد البيانات: {str(e)}', 'danger')

    return redirect(url_for('manage_databases'))

@app.route('/admin/import-all-levels', methods=['POST'])
@login_required
def import_all_levels():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    if 'file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('manage_databases'))

    file = request.files['file']

    if file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('manage_databases'))

    if not file.filename.endswith('.xlsx'):
        flash('يجب أن يكون الملف بصيغة Excel (.xlsx)', 'danger')
        return redirect(url_for('manage_databases'))

    try:
        # قراءة ملف Excel
        df = pd.read_excel(file, sheet_name=None)  # قراءة جميع الأوراق

        # استيراد المستويات
        if 'Levels' in df:
            levels_df = df['Levels']
            for _, row in levels_df.iterrows():
                row_dict = row.to_dict()
                level_name = row_dict.get('name', '')
                if level_name:
                    # التحقق من وجود المستوى
                    level = EducationalLevel.query.filter_by(name=level_name).first()
                    if not level:
                        level = EducationalLevel(name=level_name)
                        db.session.add(level)
                        db.session.flush()  # للحصول على معرف المستوى

                    # التحقق من وجود قاعدة بيانات نشطة لهذا المستوى
                    db_exists = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
                    if not db_exists:
                        # إنشاء قاعدة بيانات جديدة للمستوى
                        db_name = f"{level_name}_DB"
                        new_db = LevelDatabase(
                            name=db_name,
                            level_id=level.id,
                            file_path=f"databases/{db_name}.db",
                            is_active=True
                        )
                        db.session.add(new_db)
                        db.session.flush()  # للحصول على معرف قاعدة البيانات

        # حفظ التغييرات
        db.session.commit()
        flash('تم استيراد المستويات بنجاح', 'success')

        # استيراد البيانات لكل قاعدة بيانات
        if 'Hierarchical_Data' in df:
            hierarchical_data = df['Hierarchical_Data']

            # إنشاء قواميس لتخزين العناصر التي تم إنشاؤها
            created_subjects = {}
            created_domains = {}
            created_materials = {}

            for _, row in hierarchical_data.iterrows():
                row_dict = row.to_dict()

                # الحصول على المستوى
                level_name = row_dict.get('level_name', '')
                if not level_name:
                    continue

                level = EducationalLevel.query.filter_by(name=level_name).first()
                if not level:
                    continue

                # الحصول على قاعدة البيانات النشطة للمستوى
                database = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
                if not database:
                    continue

                # إنشاء المادة الدراسية إذا لم تكن موجودة
                subject_name = row_dict.get('subject_name', '')
                if not subject_name:
                    continue

                subject_key = f"{database.id}_{subject_name}"
                if subject_key not in created_subjects:
                    subject = LevelDataEntry.query.filter_by(
                        database_id=database.id,
                        entry_type='subject',
                        name=subject_name
                    ).first()

                    if not subject:
                        subject = LevelDataEntry(
                            database_id=database.id,
                            entry_type='subject',
                            name=subject_name,
                            is_active=True
                        )
                        db.session.add(subject)
                        db.session.flush()

                    created_subjects[subject_key] = subject.id

                subject_id = created_subjects[subject_key]

                # إنشاء الميدان إذا لم يكن موجوداً
                domain_name = row_dict.get('domain_name', '')
                if not domain_name:
                    continue

                domain_key = f"{database.id}_{domain_name}_{subject_id}"
                if domain_key not in created_domains:
                    domain = LevelDataEntry.query.filter_by(
                        database_id=database.id,
                        entry_type='domain',
                        name=domain_name,
                        parent_id=subject_id
                    ).first()

                    if not domain:
                        domain = LevelDataEntry(
                            database_id=database.id,
                            entry_type='domain',
                            name=domain_name,
                            parent_id=subject_id,
                            is_active=True
                        )
                        db.session.add(domain)
                        db.session.flush()

                    created_domains[domain_key] = domain.id

                domain_id = created_domains[domain_key]

                # إنشاء المادة المعرفية إذا لم تكن موجودة
                material_name = row_dict.get('material_name', '')
                if not material_name:
                    continue

                material_key = f"{database.id}_{material_name}_{domain_id}"
                if material_key not in created_materials:
                    material = LevelDataEntry.query.filter_by(
                        database_id=database.id,
                        entry_type='material',
                        name=material_name,
                        parent_id=domain_id
                    ).first()

                    if not material:
                        material = LevelDataEntry(
                            database_id=database.id,
                            entry_type='material',
                            name=material_name,
                            parent_id=domain_id,
                            is_active=True
                        )
                        db.session.add(material)
                        db.session.flush()

                    created_materials[material_key] = material.id

                material_id = created_materials[material_key]

                # إنشاء الكفاءة المستهدفة
                competency_name = row_dict.get('competency_name', '')
                competency_description = row_dict.get('competency_description', '')

                if not competency_name and not competency_description:
                    continue

                # التحقق من وجود الكفاءة
                competency = LevelDataEntry.query.filter_by(
                    database_id=database.id,
                    entry_type='competency',
                    name=competency_name,
                    parent_id=material_id
                ).first()

                if not competency:
                    competency = LevelDataEntry(
                        database_id=database.id,
                        entry_type='competency',
                        name=competency_name,
                        description=competency_description,
                        parent_id=material_id,
                        is_active=True
                    )
                    db.session.add(competency)

            # حفظ التغييرات
            db.session.commit()
            flash('تم استيراد البيانات الهرمية بنجاح', 'success')

        return redirect(url_for('manage_databases'))

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء استيراد البيانات: {str(e)}', 'danger')
        return redirect(url_for('manage_databases'))

@app.route('/admin/databases/<int:db_id>/import', methods=['POST'])
@login_required
def import_database_data(db_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    database = LevelDatabase.query.get_or_404(db_id)

    if 'file' not in request.files:
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('view_database', db_id=db_id))

    file = request.files['file']
    import_type = request.form.get('import_type', 'all')
    clear_existing = 'clear_existing' in request.form

    if file.filename == '':
        flash('لم يتم تحديد ملف', 'danger')
        return redirect(url_for('view_database', db_id=db_id))

    if not file.filename.endswith('.xlsx'):
        flash('يجب أن يكون الملف بصيغة Excel (.xlsx)', 'danger')
        return redirect(url_for('view_database', db_id=db_id))

    try:
        # قراءة ملف Excel
        df = pd.read_excel(file, sheet_name=None)  # قراءة جميع الأوراق

        # حذف البيانات الحالية (افتراضياً) لتجنب التكرار
        # إذا لم يتم تحديد clear_existing، فسيتم حذف البيانات افتراضياً
        if clear_existing or not request.form.get('keep_existing'):
            if import_type == 'all':
                # حذف جميع البيانات
                deleted_count = LevelDataEntry.query.filter_by(database_id=db_id).count()
                LevelDataEntry.query.filter_by(database_id=db_id).delete()
                db.session.commit()
                flash(f'تم حذف {deleted_count} عنصر من البيانات الحالية لتجنب التكرار', 'info')
            elif import_type in ['subject', 'domain', 'material', 'competency']:
                # حذف نوع محدد من البيانات
                deleted_count = LevelDataEntry.query.filter_by(database_id=db_id, entry_type=import_type).count()
                LevelDataEntry.query.filter_by(database_id=db_id, entry_type=import_type).delete()
                db.session.commit()
                flash(f'تم حذف {deleted_count} عنصر من بيانات {import_type} الحالية لتجنب التكرار', 'info')

        # استيراد البيانات المترابطة
        if import_type == 'hierarchical' and 'Hierarchical_Data' in df:
            hierarchical_data = df['Hierarchical_Data']

            # إنشاء قاموس لتخزين العناصر التي تم إنشاؤها
            created_subjects = {}
            created_domains = {}
            created_materials = {}

            for _, row in hierarchical_data.iterrows():
                row_dict = row.to_dict()

                # إنشاء المادة الدراسية إذا لم تكن موجودة
                subject_name = row_dict.get('subject_name', '')
                if subject_name and subject_name not in created_subjects:
                    subject = LevelDataEntry.query.filter_by(
                        database_id=db_id,
                        entry_type='subject',
                        name=subject_name
                    ).first()

                    if not subject:
                        subject = LevelDataEntry(
                            database_id=db_id,
                            entry_type='subject',
                            name=subject_name,
                            description='',
                            is_active=True
                        )
                        db.session.add(subject)
                        db.session.flush()  # للحصول على معرف العنصر

                    created_subjects[subject_name] = subject.id

                # إنشاء الميدان إذا لم يكن موجوداً
                domain_name = row_dict.get('domain_name', '')
                if domain_name and domain_name not in created_domains and subject_name in created_subjects:
                    domain = LevelDataEntry.query.filter_by(
                        database_id=db_id,
                        entry_type='domain',
                        name=domain_name
                    ).first()

                    if not domain:
                        domain = LevelDataEntry(
                            database_id=db_id,
                            entry_type='domain',
                            parent_id=created_subjects[subject_name],
                            name=domain_name,
                            description='',
                            is_active=True
                        )
                        db.session.add(domain)
                        db.session.flush()

                    created_domains[domain_name] = domain.id

                # إنشاء المادة المعرفية إذا لم تكن موجودة
                material_name = row_dict.get('material_name', '')
                if material_name and material_name not in created_materials and domain_name in created_domains:
                    material = LevelDataEntry.query.filter_by(
                        database_id=db_id,
                        entry_type='material',
                        name=material_name
                    ).first()

                    if not material:
                        material = LevelDataEntry(
                            database_id=db_id,
                            entry_type='material',
                            parent_id=created_domains[domain_name],
                            name=material_name,
                            description='',
                            is_active=True
                        )
                        db.session.add(material)
                        db.session.flush()

                    created_materials[material_name] = material.id

                # إنشاء الكفاءة المستهدفة
                competency_name = row_dict.get('competency_name', '')
                competency_description = row_dict.get('competency_description', '')
                if competency_name and material_name in created_materials:
                    competency = LevelDataEntry(
                        database_id=db_id,
                        entry_type='competency',
                        parent_id=created_materials[material_name],
                        name=competency_name,
                        description=competency_description,
                        is_active=True
                    )
                    db.session.add(competency)

            db.session.commit()
            flash('تم استيراد البيانات المترابطة بنجاح', 'success')
            return redirect(url_for('view_database', db_id=db_id))

        # استيراد نوع محدد من البيانات
        if import_type != 'all' and import_type != 'hierarchical':
            sheet_names = {
                'subject': ['Subjects', 'subjects'],
                'domain': ['Domains', 'domains'],
                'material': ['Materials', 'materials'],
                'competency': ['Competencies', 'competencies']
            }

            # البحث عن الورقة المناسبة
            sheet_found = False
            for sheet_name in sheet_names[import_type]:
                if sheet_name in df:
                    sheet_data = df[sheet_name]
                    sheet_found = True

                    for _, row in sheet_data.iterrows():
                        row_dict = row.to_dict()

                        # الحصول على معرف الأب
                        parent_id = None
                        if import_type != 'subject':
                            if 'parent_id' in row_dict and pd.notna(row_dict['parent_id']):
                                parent_id = int(row_dict['parent_id'])
                            elif 'parent_name' in row_dict and pd.notna(row_dict['parent_name']):
                                parent_name = row_dict['parent_name']
                                parent_type = {
                                    'domain': 'subject',
                                    'material': 'domain',
                                    'competency': 'material'
                                }[import_type]

                                parent = LevelDataEntry.query.filter_by(
                                    database_id=db_id,
                                    entry_type=parent_type,
                                    name=parent_name
                                ).first()

                                if parent:
                                    parent_id = parent.id

                        # إنشاء عنصر جديد
                        name = row_dict.get('name', '')
                        description = row_dict.get('description', '')
                        is_active = row_dict.get('is_active', True)

                        if name:
                            new_entry = LevelDataEntry(
                                database_id=db_id,
                                entry_type=import_type,
                                parent_id=parent_id,
                                name=name,
                                description=description,
                                is_active=is_active
                            )

                            db.session.add(new_entry)

                    db.session.commit()
                    flash(f'تم استيراد بيانات {import_type} بنجاح', 'success')
                    break

            if not sheet_found:
                flash(f'لم يتم العثور على ورقة {import_type} في الملف', 'warning')

            return redirect(url_for('view_database', db_id=db_id))

        # استيراد جميع البيانات
        if import_type == 'all':
            # معالجة كل ورقة
            for sheet_name, sheet_data in df.items():
                if sheet_name.lower() in ['subjects', 'domains', 'materials', 'competencies']:
                    entry_type = sheet_name.lower().rstrip('s')  # إزالة حرف 's' من النهاية

                    for _, row in sheet_data.iterrows():
                        # تحويل الصف إلى قاموس
                        row_dict = row.to_dict()

                        # الحصول على معرف الأب
                        parent_id = None
                        if entry_type != 'subject':
                            if 'parent_id' in row_dict and pd.notna(row_dict['parent_id']):
                                parent_id = int(row_dict['parent_id'])
                            elif 'parent_name' in row_dict and pd.notna(row_dict['parent_name']):
                                parent_name = row_dict['parent_name']
                                parent_type = {
                                    'domain': 'subject',
                                    'material': 'domain',
                                    'competency': 'material'
                                }[entry_type]

                                parent = LevelDataEntry.query.filter_by(
                                    database_id=db_id,
                                    entry_type=parent_type,
                                    name=parent_name
                                ).first()

                                if parent:
                                    parent_id = parent.id

                        # إنشاء عنصر جديد
                        name = row_dict.get('name', '')
                        description = row_dict.get('description', '')
                        is_active = row_dict.get('is_active', True)

                        if name:
                            new_entry = LevelDataEntry(
                                database_id=db_id,
                                entry_type=entry_type,
                                parent_id=parent_id,
                                name=name,
                                description=description,
                                is_active=is_active
                            )

                            db.session.add(new_entry)

            db.session.commit()
            flash('تم استيراد جميع البيانات بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء استيراد البيانات: {str(e)}', 'danger')

    return redirect(url_for('view_database', db_id=db_id))

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج', 'info')
    return redirect(url_for('index'))

# الملف الشخصي
@app.route('/profile')
@login_required
def profile():
    return render_template('profile.html', user=current_user)

# تحديث الملف الشخصي
@app.route('/profile/update', methods=['POST'])
@login_required
def update_profile():
    username = request.form.get('username')
    email = request.form.get('email')
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')

    if not username or not email:
        flash('اسم المستخدم والبريد الإلكتروني مطلوبان', 'danger')
        return redirect(url_for('profile'))

    # التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم أو البريد الإلكتروني
    existing_user = User.query.filter(
        (User.username == username) | (User.email == email),
        User.id != current_user.id
    ).first()

    if existing_user:
        if existing_user.username == username:
            flash('اسم المستخدم مستخدم بالفعل', 'danger')
        else:
            flash('البريد الإلكتروني مستخدم بالفعل', 'danger')
        return redirect(url_for('profile'))

    # تحديث البيانات الأساسية
    current_user.username = username
    current_user.email = email

    # تحديث كلمة المرور إذا تم إدخالها
    if new_password:
        if not current_password:
            flash('يجب إدخال كلمة المرور الحالية لتغيير كلمة المرور', 'danger')
            return redirect(url_for('profile'))

        if not check_password_hash(current_user.password, current_password):
            flash('كلمة المرور الحالية غير صحيحة', 'danger')
            return redirect(url_for('profile'))

        if new_password != confirm_password:
            flash('كلمة المرور الجديدة وتأكيدها غير متطابقتين', 'danger')
            return redirect(url_for('profile'))

        if len(new_password) < 6:
            flash('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'danger')
            return redirect(url_for('profile'))

        current_user.password = generate_password_hash(new_password)

    try:
        db.session.commit()
        flash('تم تحديث الملف الشخصي بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تحديث الملف الشخصي', 'danger')

    return redirect(url_for('profile'))

# إنشاء مستخدم جديد من قبل الإدارة
@app.route('/admin/create-user', methods=['POST'])
@login_required
def admin_create_user():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    username = request.form.get('username')
    email = request.form.get('email')
    password = request.form.get('password')
    role = request.form.get('role')
    phone_number = request.form.get('phone_number')
    wilaya_code = request.form.get('wilaya_code')

    if not username or not email or not password or not role or not phone_number:
        flash('جميع الحقول مطلوبة عدا الولاية', 'danger')
        return redirect(url_for('admin_dashboard'))

    # التحقق من صحة رقم الهاتف
    if not validate_algerian_phone(phone_number):
        flash('رقم الهاتف غير صحيح. يرجى إدخال رقم هاتف جزائري صحيح', 'danger')
        return redirect(url_for('admin_dashboard'))

    # التحقق من وجود اسم المستخدم أو البريد الإلكتروني
    if User.query.filter_by(username=username).first():
        flash('اسم المستخدم موجود بالفعل', 'danger')
        return redirect(url_for('admin_dashboard'))

    if User.query.filter_by(email=email).first():
        flash('البريد الإلكتروني موجود بالفعل', 'danger')
        return redirect(url_for('admin_dashboard'))

    # إنشاء مستخدم جديد (مفعل بشكل افتراضي عند الإنشاء من قبل الإدارة)
    hashed_password = generate_password_hash(password)
    new_user = User(
        username=username,
        email=email,
        password=hashed_password,
        role=role,
        phone_number=phone_number,
        wilaya_code=wilaya_code if wilaya_code else None,
        _is_active=True  # مفعل بشكل افتراضي
    )

    try:
        db.session.add(new_user)
        db.session.commit()

        role_name = {
            Role.ADMIN: 'إدارة',
            Role.INSPECTOR: 'مفتش',
            Role.TEACHER: 'أستاذ',
            Role.USER_MANAGER: 'مدير المستخدمين'
        }.get(role, role)

        flash(f'تم إنشاء حساب {role_name} جديد بنجاح: {username}', 'success')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء إنشاء الحساب', 'danger')

    return redirect(url_for('admin_dashboard'))

# إنشاء حساب مدير مستخدمين جديد (للأدمن فقط)
@app.route('/admin/create-user-manager', methods=['POST'])
@login_required
def admin_create_user_manager():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    username = request.form.get('username')
    email = request.form.get('email')
    password = request.form.get('password')
    phone_number = request.form.get('phone_number')
    wilaya_code = request.form.get('wilaya_code')

    if not username or not email or not password or not phone_number:
        flash('جميع الحقول مطلوبة عدا الولاية', 'danger')
        return redirect(url_for('admin_dashboard'))

    # التحقق من صحة رقم الهاتف
    if not validate_algerian_phone(phone_number):
        flash('رقم الهاتف غير صحيح. يرجى إدخال رقم هاتف جزائري صحيح', 'danger')
        return redirect(url_for('admin_dashboard'))

    # التحقق من وجود اسم المستخدم أو البريد الإلكتروني
    if User.query.filter_by(username=username).first():
        flash('اسم المستخدم موجود بالفعل', 'danger')
        return redirect(url_for('admin_dashboard'))

    if User.query.filter_by(email=email).first():
        flash('البريد الإلكتروني موجود بالفعل', 'danger')
        return redirect(url_for('admin_dashboard'))

    # إنشاء مستخدم جديد بدور مدير المستخدمين
    hashed_password = generate_password_hash(password)
    new_user = User(
        username=username,
        email=email,
        password=hashed_password,
        role=Role.USER_MANAGER,
        phone_number=phone_number,
        wilaya_code=wilaya_code if wilaya_code else None,
        _is_active=True
    )

    try:
        db.session.add(new_user)
        db.session.commit()
        flash(f'تم إنشاء حساب مدير المستخدمين بنجاح: {username}', 'success')
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء إنشاء الحساب', 'danger')

    return redirect(url_for('admin_dashboard'))

# إدارة إعدادات الأدوار المتاحة للتسجيل
@app.route('/admin/role-settings', methods=['GET', 'POST'])
@login_required
def admin_role_settings():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        # تحديث إعدادات الأدوار
        teacher_enabled = request.form.get('teacher_enabled') == 'on'
        inspector_enabled = request.form.get('inspector_enabled') == 'on'

        # تحديث أو إنشاء إعدادات دور المعلم
        teacher_setting = RoleSettings.query.filter_by(role_name=Role.TEACHER).first()
        if teacher_setting:
            teacher_setting.is_enabled = teacher_enabled
        else:
            teacher_setting = RoleSettings(
                role_name=Role.TEACHER,
                display_name='أستاذ',
                is_enabled=teacher_enabled
            )
            db.session.add(teacher_setting)

        # تحديث أو إنشاء إعدادات دور المفتش
        inspector_setting = RoleSettings.query.filter_by(role_name=Role.INSPECTOR).first()
        if inspector_setting:
            inspector_setting.is_enabled = inspector_enabled
        else:
            inspector_setting = RoleSettings(
                role_name=Role.INSPECTOR,
                display_name='مفتش',
                is_enabled=inspector_enabled
            )
            db.session.add(inspector_setting)

        try:
            db.session.commit()
            flash('تم تحديث إعدادات الأدوار بنجاح', 'success')
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث الإعدادات', 'danger')

        return redirect(url_for('admin_dashboard'))

    # الحصول على الإعدادات الحالية
    teacher_setting = RoleSettings.query.filter_by(role_name=Role.TEACHER).first()
    inspector_setting = RoleSettings.query.filter_by(role_name=Role.INSPECTOR).first()

    settings = {
        'teacher_enabled': teacher_setting.is_enabled if teacher_setting else True,
        'inspector_enabled': inspector_setting.is_enabled if inspector_setting else True
    }

    return jsonify(settings)

# تفعيل جميع حسابات المعلمين (للأدمن فقط)
@app.route('/admin/activate-all-teachers', methods=['POST'])
@login_required
def admin_activate_all_teachers():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # تفعيل جميع حسابات المعلمين
        teachers = User.query.filter_by(role=Role.TEACHER).all()
        activated_count = 0

        for teacher in teachers:
            if not teacher.is_active:
                teacher.is_active = True
                activated_count += 1

        db.session.commit()

        if activated_count > 0:
            flash(f'تم تفعيل {activated_count} حساب معلم بنجاح', 'success')
        else:
            flash('جميع حسابات المعلمين مفعلة بالفعل', 'info')

    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تفعيل الحسابات', 'danger')

    return redirect(url_for('admin_dashboard'))

# تعطيل جميع حسابات المعلمين (للأدمن فقط)
@app.route('/admin/deactivate-all-teachers', methods=['POST'])
@login_required
def admin_deactivate_all_teachers():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # تعطيل جميع حسابات المعلمين
        teachers = User.query.filter_by(role=Role.TEACHER).all()
        deactivated_count = 0

        for teacher in teachers:
            if teacher.is_active:
                teacher.is_active = False
                deactivated_count += 1

        db.session.commit()

        if deactivated_count > 0:
            flash(f'تم تعطيل {deactivated_count} حساب معلم بنجاح', 'success')
        else:
            flash('جميع حسابات المعلمين معطلة بالفعل', 'info')

    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء تعطيل الحسابات', 'danger')

    return redirect(url_for('admin_dashboard'))

# لوحة تحكم مدير المستخدمين
@app.route('/dashboard/user-manager')
@login_required
def user_manager_dashboard():
    if current_user.role != Role.USER_MANAGER:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على جميع المستخدمين (عدا الأدمن)
    all_users = User.query.filter(User.role != Role.ADMIN).all()
    teachers = User.query.filter_by(role=Role.TEACHER).all()
    inspectors = User.query.filter_by(role=Role.INSPECTOR).all()
    user_managers = User.query.filter_by(role=Role.USER_MANAGER).all()

    # إحصائيات المستخدمين
    stats = {
        'total_users': len(all_users),
        'active_users': len([u for u in all_users if u.is_active]),
        'inactive_users': len([u for u in all_users if not u.is_active]),
        'teachers_count': len(teachers),
        'inspectors_count': len(inspectors),
        'user_managers_count': len(user_managers)
    }

    return render_template('user_manager_dashboard.html',
                         all_users=all_users,
                         teachers=teachers,
                         inspectors=inspectors,
                         user_managers=user_managers,
                         stats=stats)

# تفعيل مستخدم واحد (لمدير المستخدمين)
@app.route('/user-manager/activate-user/<int:user_id>', methods=['POST'])
@login_required
def user_manager_activate_user(user_id):
    if current_user.role not in [Role.USER_MANAGER, Role.ADMIN]:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(user_id)

    # لا يمكن تعديل حسابات الأدمن
    if user.role == Role.ADMIN:
        flash('لا يمكن تعديل حسابات المديرين', 'danger')
        return redirect(url_for('users_list'))

    # مدير المستخدمين لا يمكنه تفعيل مديري المستخدمين الآخرين
    if current_user.role == Role.USER_MANAGER and user.role == Role.USER_MANAGER:
        flash('لا يمكن لمدير المستخدمين تفعيل مديري المستخدمين الآخرين', 'danger')
        return redirect(url_for('users_list'))

    if user.is_active:
        flash(f'حساب {user.username} مفعل بالفعل', 'info')
    else:
        user.is_active = True
        db.session.commit()
        flash(f'تم تفعيل حساب {user.username} بنجاح', 'success')

    return redirect(url_for('users_list'))

# تعطيل مستخدم واحد (لمدير المستخدمين)
@app.route('/user-manager/deactivate-user/<int:user_id>', methods=['POST'])
@login_required
def user_manager_deactivate_user(user_id):
    if current_user.role not in [Role.USER_MANAGER, Role.ADMIN]:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(user_id)

    # لا يمكن تعديل حسابات الأدمن
    if user.role == Role.ADMIN:
        flash('لا يمكن تعديل حسابات المديرين', 'danger')
        return redirect(url_for('users_list'))

    # مدير المستخدمين لا يمكنه تعطيل مديري المستخدمين الآخرين
    if current_user.role == Role.USER_MANAGER and user.role == Role.USER_MANAGER:
        flash('لا يمكن لمدير المستخدمين تعطيل مديري المستخدمين الآخرين', 'danger')
        return redirect(url_for('users_list'))

    # لا يمكن تعطيل الحساب الخاص
    if user.id == current_user.id:
        flash('لا يمكنك تعطيل حسابك الخاص', 'danger')
        return redirect(url_for('users_list'))

    if not user.is_active:
        flash(f'حساب {user.username} معطل بالفعل', 'info')
    else:
        user.is_active = False
        db.session.commit()
        flash(f'تم تعطيل حساب {user.username} بنجاح', 'success')

    return redirect(url_for('users_list'))

# دالة حساب نسبة الإنجاز بناءً على المواد المعرفية (بدون تخزين مؤقت للتحديث الفوري)
def calculate_progress_by_materials(user_id, level_id=None):
    """
    حساب نسبة الإنجاز للأستاذ بناءً على المواد المعرفية المكتملة (نسخة محسنة)

    Args:
        user_id: معرف المستخدم (الأستاذ)
        level_id: معرف المستوى (اختياري - إذا لم يحدد سيحسب لجميع المستويات)

    Returns:
        dict: إحصائيات التقدم
    """
    try:
        # الحصول على جداول الأستاذ (محسن)
        if level_id:
            schedules = Schedule.query.filter_by(user_id=user_id, level_id=level_id).all()
        else:
            schedules = Schedule.query.filter_by(user_id=user_id).all()

        if not schedules:
            return {
                'completion_rate': 0,
                'completed_materials': 0,
                'total_materials': 0,
                'completed_by_subject': {},
                'total_by_subject': {}
            }

        # الحصول على جميع سجلات التقدم المكتملة للمستخدم مرة واحدة
        completed_progress_entries = ProgressEntry.query.filter_by(
            user_id=user_id,
            status='completed'
        ).all()

        # إنشاء مجموعة من معرفات المواد المكتملة للبحث السريع
        completed_material_ids = set()

        # معالجة سجلات التقدم وتحديث material_id إذا لزم الأمر
        for entry in completed_progress_entries:
            if entry.material_id:
                completed_material_ids.add(entry.material_id)
            else:
                # محاولة استنتاج material_id من البيانات الأخرى
                if entry.domain_id and entry.competency_id:
                    # البحث عن المادة المعرفية المناسبة
                    material = LevelDataEntry.query.filter_by(
                        parent_id=entry.domain_id,
                        entry_type='material',
                        is_active=True
                    ).first()
                    if material:
                        entry.material_id = material.id
                        completed_material_ids.add(material.id)
                        try:
                            db.session.commit()
                        except:
                            db.session.rollback()

        # جمع معرفات المستويات التي يدرسها الأستاذ
        level_ids = list(set([schedule.level_id for schedule in schedules]))

        total_materials = 0
        completed_materials = 0
        completed_by_subject = {}
        total_by_subject = {}

        for current_level_id in level_ids:
            # الحصول على قاعدة البيانات للمستوى
            level_db = LevelDatabase.query.filter_by(level_id=current_level_id, is_active=True).first()
            if not level_db:
                continue

            # الحصول على جميع المواد الدراسية للمستوى
            subjects = LevelDataEntry.query.filter_by(
                database_id=level_db.id,
                entry_type='subject',
                is_active=True
            ).all()

            for subject in subjects:
                subject_name = subject.name

                # الحصول على المواد المعرفية للمادة
                domains = LevelDataEntry.query.filter_by(
                    database_id=level_db.id,
                    entry_type='domain',
                    parent_id=subject.id,
                    is_active=True
                ).all()

                subject_total = 0
                subject_completed = 0

                # تهيئة الإحصائيات للمادة إذا لم تكن موجودة
                if subject_name not in completed_by_subject:
                    completed_by_subject[subject_name] = 0
                    total_by_subject[subject_name] = 0

                for domain in domains:
                    materials = LevelDataEntry.query.filter_by(
                        database_id=level_db.id,
                        entry_type='material',
                        parent_id=domain.id,
                        is_active=True
                    ).all()

                    subject_total += len(materials)

                    # فحص المواد المكتملة (بحث محسن)
                    for material in materials:
                        if material.id in completed_material_ids:
                            subject_completed += 1

                # تحديث الإحصائيات
                total_by_subject[subject_name] += subject_total
                completed_by_subject[subject_name] += subject_completed
                total_materials += subject_total
                completed_materials += subject_completed

        # حساب نسبة الإنجاز
        completion_rate = 0
        if total_materials > 0:
            completion_rate = (completed_materials / total_materials) * 100

        return {
            'completion_rate': completion_rate,
            'completed_materials': completed_materials,
            'total_materials': total_materials,
            'completed_by_subject': completed_by_subject,
            'total_by_subject': total_by_subject
        }

    except Exception as e:
        print(f"Error calculating progress by materials: {str(e)}")
        return {
            'completion_rate': 0,
            'completed_materials': 0,
            'total_materials': 0,
            'completed_by_subject': {},
            'total_by_subject': {}
        }

# دالة حساب إجمالي المواد المعرفية في النظام
def calculate_total_system_materials(user_id):
    """
    حساب إجمالي المواد المعرفية في النظام وما أكمله المعلم منها

    Args:
        user_id: معرف المستخدم (الأستاذ)

    Returns:
        dict: إحصائيات شاملة لجميع المواد المعرفية في النظام
    """
    try:
        # الحصول على جميع سجلات التقدم المكتملة للمستخدم
        completed_progress_entries = ProgressEntry.query.filter_by(
            user_id=user_id,
            status='completed'
        ).all()

        # إنشاء مجموعة من معرفات المواد المكتملة للبحث السريع
        completed_material_ids = {entry.material_id for entry in completed_progress_entries if entry.material_id}

        # الحصول على جميع المستويات التعليمية النشطة
        all_levels = EducationalLevel.query.filter_by(is_active=True).all()

        total_materials = 0
        completed_materials = 0
        completed_by_subject = {}
        total_by_subject = {}

        for level in all_levels:
            # الحصول على قاعدة البيانات للمستوى
            level_db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
            if not level_db:
                continue

            # الحصول على جميع المواد الدراسية للمستوى
            subjects = LevelDataEntry.query.filter_by(
                database_id=level_db.id,
                entry_type='subject',
                is_active=True
            ).all()

            for subject in subjects:
                subject_name = f"{subject.name} - {level.name}"

                # الحصول على المواد المعرفية للمادة
                domains = LevelDataEntry.query.filter_by(
                    database_id=level_db.id,
                    entry_type='domain',
                    parent_id=subject.id,
                    is_active=True
                ).all()

                subject_total = 0
                subject_completed = 0

                # تهيئة الإحصائيات للمادة إذا لم تكن موجودة
                if subject_name not in completed_by_subject:
                    completed_by_subject[subject_name] = 0
                    total_by_subject[subject_name] = 0

                for domain in domains:
                    materials = LevelDataEntry.query.filter_by(
                        database_id=level_db.id,
                        entry_type='material',
                        parent_id=domain.id,
                        is_active=True
                    ).all()

                    subject_total += len(materials)

                    # فحص المواد المكتملة
                    for material in materials:
                        if material.id in completed_material_ids:
                            subject_completed += 1

                # تحديث الإحصائيات
                total_by_subject[subject_name] += subject_total
                completed_by_subject[subject_name] += subject_completed
                total_materials += subject_total
                completed_materials += subject_completed

        # حساب نسبة الإنجاز
        completion_rate = 0
        if total_materials > 0:
            completion_rate = (completed_materials / total_materials) * 100

        return {
            'completion_rate': completion_rate,
            'completed_materials': completed_materials,
            'total_materials': total_materials,
            'completed_by_subject': completed_by_subject,
            'total_by_subject': total_by_subject
        }

    except Exception as e:
        print(f"Error calculating total system materials: {str(e)}")
        return {
            'completion_rate': 0,
            'completed_materials': 0,
            'total_materials': 0,
            'completed_by_subject': {},
            'total_by_subject': {}
        }

# دالة مساعدة لتحديث سجلات التقدم التي تفتقر إلى material_id
def update_missing_material_ids(user_id):
    """
    تحديث سجلات التقدم التي تفتقر إلى material_id بناءً على البيانات الأخرى المتوفرة
    """
    try:
        # الحصول على سجلات التقدم التي تفتقر إلى material_id
        entries_without_material = ProgressEntry.query.filter_by(
            user_id=user_id,
            material_id=None
        ).all()

        # الحصول على السجلات المعزولة تماماً (بدون أي معلومات ربط)
        orphaned_entries = ProgressEntry.query.filter_by(
            user_id=user_id,
            level_id=None,
            material_id=None,
            domain_id=None,
            subject_id=None
        ).all()

        updated_count = 0

        # معالجة السجلات التي تحتوي على معلومات جزئية
        for entry in entries_without_material:
            material_id = None

            # محاولة استنتاج material_id من domain_id
            if entry.domain_id:
                # البحث عن أول مادة معرفية في هذا الميدان
                material = LevelDataEntry.query.filter_by(
                    parent_id=entry.domain_id,
                    entry_type='material',
                    is_active=True
                ).first()
                if material:
                    material_id = material.id

            # إذا لم نجد material_id من domain_id، نحاول من subject_id
            elif entry.subject_id:
                # البحث عن أول ميدان في هذه المادة الدراسية
                domain = LevelDataEntry.query.filter_by(
                    parent_id=entry.subject_id,
                    entry_type='domain',
                    is_active=True
                ).first()
                if domain:
                    # البحث عن أول مادة معرفية في هذا الميدان
                    material = LevelDataEntry.query.filter_by(
                        parent_id=domain.id,
                        entry_type='material',
                        is_active=True
                    ).first()
                    if material:
                        material_id = material.id
                        entry.domain_id = domain.id

            # تحديث السجل إذا وجدنا material_id
            if material_id:
                entry.material_id = material_id
                updated_count += 1

        # معالجة السجلات المعزولة تماماً
        if orphaned_entries:
            print(f"معالجة {len(orphaned_entries)} سجل معزول...")

            # الحصول على جميع المستويات النشطة
            levels = EducationalLevel.query.filter_by(is_active=True).all()

            # توزيع السجلات المعزولة على المستويات
            entries_per_level = len(orphaned_entries) // len(levels) if levels else 0
            remainder = len(orphaned_entries) % len(levels) if levels else 0

            entry_index = 0
            for level_idx, level in enumerate(levels):
                # تحديد عدد السجلات لهذا المستوى
                entries_for_this_level = entries_per_level
                if level_idx < remainder:
                    entries_for_this_level += 1

                # الحصول على قاعدة البيانات للمستوى
                level_db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
                if not level_db:
                    continue

                # الحصول على أول مادة معرفية في المستوى
                first_material = LevelDataEntry.query.filter_by(
                    database_id=level_db.id,
                    entry_type='material',
                    is_active=True
                ).first()

                if first_material:
                    # ربط السجلات بهذا المستوى
                    for i in range(entries_for_this_level):
                        if entry_index < len(orphaned_entries):
                            entry = orphaned_entries[entry_index]
                            entry.material_id = first_material.id
                            entry.level_id = level.id

                            # ربط بالميدان والمادة الدراسية
                            if first_material.parent_id:
                                entry.domain_id = first_material.parent_id
                                domain = LevelDataEntry.query.get(first_material.parent_id)
                                if domain and domain.parent_id:
                                    entry.subject_id = domain.parent_id

                            updated_count += 1
                            entry_index += 1
                            print(f"  ربط السجل {entry.id} بالمستوى {level.name}")

        if updated_count > 0:
            db.session.commit()
            print(f"Updated {updated_count} progress entries with missing material_id")

        return updated_count

    except Exception as e:
        print(f"Error updating missing material_ids: {str(e)}")
        db.session.rollback()
        return 0

# دالة لإعادة حساب جميع الإحصائيات للأستاذ
def recalculate_teacher_stats(user_id):
    """
    إعادة حساب جميع الإحصائيات للأستاذ بعد تحديث سجلات التقدم
    """
    try:
        # تحديث سجلات التقدم التي تفتقر إلى material_id
        update_missing_material_ids(user_id)

        # إعادة حساب الإحصائيات
        progress_stats = calculate_progress_by_materials(user_id)

        # الحصول على المستويات التي يدرسها الأستاذ
        schedules = Schedule.query.filter_by(user_id=user_id).all()
        level_ids = list(set([schedule.level_id for schedule in schedules]))

        level_stats = {}
        for level_id in level_ids:
            level_progress = calculate_level_progress_by_materials(user_id, level_id)
            level = EducationalLevel.query.get(level_id)
            if level:
                level_stats[level_id] = {
                    'name': level.name,
                    'completion_rate': level_progress['completion_rate'],
                    'completed_materials': level_progress['completed_materials'],
                    'total_materials': level_progress['total_materials'],
                    'completed_by_subject': level_progress['completed_by_subject'],
                    'total_by_subject': level_progress['total_by_subject']
                }

        return {
            'progress_stats': progress_stats,
            'level_stats': level_stats
        }

    except Exception as e:
        print(f"Error recalculating teacher stats: {str(e)}")
        return None

# دالة حساب نسبة الإنجاز لمستوى محدد بناءً على سجلات التقدم الفعلية
def calculate_level_progress_by_materials(user_id, level_id):
    """
    حساب نسبة الإنجاز للأستاذ في مستوى محدد بناءً على سجلات التقدم الفعلية
    بدلاً من الاعتماد على الجدول الدراسي
    """
    try:
        # الحصول على قاعدة البيانات للمستوى
        level_db = LevelDatabase.query.filter_by(level_id=level_id, is_active=True).first()
        if not level_db:
            return {
                'completion_rate': 0,
                'completed_materials': 0,
                'total_materials': 0,
                'completed_by_subject': {},
                'total_by_subject': {}
            }

        # الحصول على جميع سجلات التقدم للمستخدم
        progress_entries = ProgressEntry.query.filter_by(user_id=user_id).all()

        # فلترة السجلات التي تنتمي لهذا المستوى
        level_progress_entries = []
        orphaned_entries = []  # السجلات التي تفتقر إلى معلومات الربط

        for entry in progress_entries:
            # التحقق من انتماء السجل لهذا المستوى بطرق متعددة
            belongs_to_level = False

            # الطريقة الأولى: التحقق من level_id مباشرة
            if entry.level_id == level_id:
                belongs_to_level = True

            # الطريقة الثانية: التحقق من material_id
            elif entry.material_id:
                material = LevelDataEntry.query.filter_by(
                    id=entry.material_id,
                    database_id=level_db.id,
                    entry_type='material'
                ).first()
                if material:
                    belongs_to_level = True
                    # تحديث level_id إذا لم يكن محدد
                    if not entry.level_id:
                        entry.level_id = level_id
                        db.session.commit()

            # الطريقة الثالثة: التحقق من domain_id
            elif entry.domain_id:
                domain = LevelDataEntry.query.filter_by(
                    id=entry.domain_id,
                    database_id=level_db.id,
                    entry_type='domain'
                ).first()
                if domain:
                    belongs_to_level = True
                    # تحديث level_id إذا لم يكن محدد
                    if not entry.level_id:
                        entry.level_id = level_id
                        db.session.commit()

            # الطريقة الرابعة: التحقق من subject_id
            elif entry.subject_id:
                subject = LevelDataEntry.query.filter_by(
                    id=entry.subject_id,
                    database_id=level_db.id,
                    entry_type='subject'
                ).first()
                if subject:
                    belongs_to_level = True
                    # تحديث level_id إذا لم يكن محدد
                    if not entry.level_id:
                        entry.level_id = level_id
                        db.session.commit()

            # إذا لم نجد أي ربط، نضعه في قائمة السجلات المعزولة
            elif not entry.level_id and not entry.material_id and not entry.domain_id and not entry.subject_id:
                orphaned_entries.append(entry)

            if belongs_to_level:
                level_progress_entries.append(entry)

        # محاولة ربط السجلات المعزولة بالمستوى الحالي إذا كان هناك مؤشرات
        # (مثل التاريخ أو ترتيب الإنشاء)
        if orphaned_entries and len(level_progress_entries) > 0:
            # توزيع السجلات المعزولة على المستويات بناءً على النسبة
            total_levels = EducationalLevel.query.count()
            orphaned_for_this_level = len(orphaned_entries) // total_levels

            # إضافة السجلات المعزولة للمستوى الحالي
            for i, entry in enumerate(orphaned_entries[:orphaned_for_this_level]):
                # ربط السجل بأول مادة معرفية في المستوى
                first_material = LevelDataEntry.query.filter_by(
                    database_id=level_db.id,
                    entry_type='material',
                    is_active=True
                ).first()

                if first_material:
                    entry.material_id = first_material.id
                    entry.level_id = level_id

                    # ربط بالميدان والمادة الدراسية
                    if first_material.parent_id:
                        entry.domain_id = first_material.parent_id
                        domain = LevelDataEntry.query.get(first_material.parent_id)
                        if domain and domain.parent_id:
                            entry.subject_id = domain.parent_id

                    level_progress_entries.append(entry)
                    try:
                        db.session.commit()
                    except:
                        db.session.rollback()

        # إنشاء مجموعة من معرفات المواد المكتملة
        completed_material_ids = set()
        for entry in level_progress_entries:
            if entry.status == 'completed' and entry.material_id:
                completed_material_ids.add(entry.material_id)



        # الحصول على جميع المواد الدراسية للمستوى
        subjects = LevelDataEntry.query.filter_by(
            database_id=level_db.id,
            entry_type='subject',
            is_active=True
        ).all()

        total_materials = 0
        completed_materials = 0
        completed_by_subject = {}
        total_by_subject = {}

        for subject in subjects:
            subject_name = subject.name

            # الحصول على المواد المعرفية للمادة
            domains = LevelDataEntry.query.filter_by(
                database_id=level_db.id,
                entry_type='domain',
                parent_id=subject.id,
                is_active=True
            ).all()

            subject_total = 0
            subject_completed = 0

            for domain in domains:
                materials = LevelDataEntry.query.filter_by(
                    database_id=level_db.id,
                    entry_type='material',
                    parent_id=domain.id,
                    is_active=True
                ).all()

                # حساب جميع المواد المعرفية للمادة الدراسية (وليس فقط التي لها سجلات تقدم)
                for material in materials:
                    subject_total += 1
                    total_materials += 1

                    # التحقق من الإكمال (فقط إذا كان هناك سجل تقدم مكتمل)
                    if material.id in completed_material_ids:
                        subject_completed += 1
                        completed_materials += 1

            # تحديث الإحصائيات للمادة (حتى لو لم تكن لها سجلات تقدم)
            completed_by_subject[subject_name] = subject_completed
            total_by_subject[subject_name] = subject_total

        # حساب نسبة الإنجاز الإجمالية للمستوى
        completion_rate = 0
        if total_materials > 0:
            completion_rate = (completed_materials / total_materials) * 100

        return {
            'completion_rate': completion_rate,
            'completed_materials': completed_materials,
            'total_materials': total_materials,
            'completed_by_subject': completed_by_subject,
            'total_by_subject': total_by_subject
        }

    except Exception as e:
        print(f"Error calculating level progress by materials: {str(e)}")
        return {
            'completion_rate': 0,
            'completed_materials': 0,
            'total_materials': 0,
            'completed_by_subject': {},
            'total_by_subject': {}
        }

# دالة تعميم النظام على جميع المستويات
def initialize_all_levels_progress_system():
    """
    تهيئة نظام حساب التقدم لجميع المستويات التعليمية
    """
    try:
        levels = EducationalLevel.query.all()
        total_materials_all_levels = 0

        print("=== تهيئة نظام التقدم لجميع المستويات ===\n")

        for level in levels:
            level_db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
            if level_db:
                # حساب عدد المواد المعرفية للمستوى
                materials_count = LevelDataEntry.query.filter_by(
                    database_id=level_db.id,
                    entry_type='material',
                    is_active=True
                ).count()

                total_materials_all_levels += materials_count

                print(f"📚 {level.name}: {materials_count} مادة معرفية")
            else:
                print(f"⚠️ {level.name}: لا توجد قاعدة بيانات")

        print(f"\n📊 إجمالي المواد المعرفية في جميع المستويات: {total_materials_all_levels}")
        print("✅ النظام جاهز لحساب التقدم بناءً على المواد المعرفية")

        return total_materials_all_levels

    except Exception as e:
        print(f"❌ خطأ في تهيئة النظام: {str(e)}")
        return 0

# دالة حساب إحصائيات شاملة لجميع الأساتذة
def calculate_all_teachers_progress_stats():
    """
    حساب إحصائيات التقدم لجميع الأساتذة بناءً على المواد المعرفية
    """
    try:
        teachers = User.query.filter_by(role=Role.TEACHER).all()
        stats = {
            'total_teachers': len(teachers),
            'teachers_with_progress': 0,
            'total_completed_materials': 0,
            'total_available_materials': 0,
            'average_completion_rate': 0,
            'teachers_stats': []
        }

        for teacher in teachers:
            teacher_progress = calculate_progress_by_materials(teacher.id)

            if teacher_progress['total_materials'] > 0:
                stats['teachers_with_progress'] += 1
                stats['total_completed_materials'] += teacher_progress['completed_materials']
                stats['total_available_materials'] += teacher_progress['total_materials']

                stats['teachers_stats'].append({
                    'teacher_id': teacher.id,
                    'teacher_name': teacher.username,
                    'completion_rate': teacher_progress['completion_rate'],
                    'completed_materials': teacher_progress['completed_materials'],
                    'total_materials': teacher_progress['total_materials']
                })

        # حساب متوسط نسبة الإنجاز
        if stats['total_available_materials'] > 0:
            stats['average_completion_rate'] = (stats['total_completed_materials'] / stats['total_available_materials']) * 100

        return stats

    except Exception as e:
        print(f"Error calculating all teachers progress stats: {str(e)}")
        return None

# إنشاء قاعدة البيانات وإضافة بيانات تجريبية
def create_sample_data():
    with app.app_context():
        # إنشاء الجداول
        db.create_all()

        # إضافة المستخدمين
        if User.query.count() == 0:
            admin = User(
                username='admin',
                email='<EMAIL>',
                password=generate_password_hash('admin123'),
                role=Role.ADMIN
            )
            inspector = User(
                username='inspector',
                email='<EMAIL>',
                password=generate_password_hash('inspector123'),
                role=Role.INSPECTOR
            )
            teacher1 = User(
                username='teacher',
                email='<EMAIL>',
                password=generate_password_hash('teacher123'),
                role=Role.TEACHER
            )
            teacher2 = User(
                username='teacher2',
                email='<EMAIL>',
                password=generate_password_hash('teacher123'),
                role=Role.TEACHER
            )

            db.session.add_all([admin, inspector, teacher1, teacher2])
            db.session.commit()

            # إضافة علاقة بين المفتش والأساتذة
            inspector.supervised_teachers.append(teacher1)
            inspector.supervised_teachers.append(teacher2)
            db.session.commit()

        # إضافة المستويات التعليمية
        if EducationalLevel.query.count() == 0:
            levels = [
                EducationalLevel(name='السنة الأولى ابتدائي', is_active=True, database_prefix='level1'),
                EducationalLevel(name='السنة الثانية ابتدائي', is_active=True, database_prefix='level2'),
                EducationalLevel(name='السنة الثالثة ابتدائي', is_active=True, database_prefix='level3'),
                EducationalLevel(name='السنة الرابعة ابتدائي', is_active=True, database_prefix='level4'),
                EducationalLevel(name='السنة الخامسة ابتدائي', is_active=True, database_prefix='level5')
            ]

            db.session.add_all(levels)
            db.session.commit()

        # إضافة قواعد البيانات المنفصلة
        if LevelDatabase.query.count() == 0:
            data_dir = os.path.join(app.root_path, 'data')
            os.makedirs(data_dir, exist_ok=True)

            for level in EducationalLevel.query.all():
                db_name = f"level_{level.id}_db"
                file_path = f"data/{db_name}.sqlite"

                new_db = LevelDatabase(
                    level_id=level.id,
                    name=f"قاعدة بيانات {level.name}",
                    file_path=file_path,
                    is_active=True
                )
                db.session.add(new_db)

            db.session.commit()

        print("تم إنشاء البيانات التجريبية بنجاح!")

# ===== الصفحات القديمة محذوفة - نستخدم النظام الجديد الموحد فقط =====




# ===== الدالة القديمة محذوفة - نستخدم get_unread_notifications_count_for_user =====

# تعديل دالة context_processor لإضافة عدد الإشعارات غير المقروءة
@app.context_processor
def inject_unread_notifications():
    if current_user.is_authenticated:
        unread_count = get_unread_notifications_count_for_user(current_user.id)
        return {'unread_notifications_count': unread_count}
    return {'unread_notifications_count': 0}

# تحضير خطة درس باستخدام الذكاء الاصطناعي
@app.route('/prepare-lesson/<int:entry_id>')
@login_required
def prepare_lesson(entry_id):
    # التحقق من أن المستخدم هو أستاذ
    if current_user.role != Role.TEACHER:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على سجل التقدم
    entry = ProgressEntry.query.get_or_404(entry_id)

    # التحقق من أن السجل ينتمي للأستاذ الحالي
    if entry.user_id != current_user.id:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على المعلومات اللازمة لتحضير الدرس
    level_name = 'غير محدد'
    subject_name = 'غير محدد'
    domain_name = 'غير محدد'
    material_name = 'غير محدد'

    # الحصول على المعلومات من قاعدة البيانات الجديدة
    if entry.competency:
        # الحصول على الكفاءة
        competency = entry.competency

        # الحصول على المادة المعرفية
        knowledge_material = KnowledgeMaterial.query.get(competency.knowledge_material_id)
        if knowledge_material:
            material_name = knowledge_material.name

            # الحصول على الميدان
            domain = Domain.query.get(knowledge_material.domain_id)
            if domain:
                domain_name = domain.name

                # الحصول على المادة
                subject = Subject.query.get(domain.subject_id)
                if subject:
                    subject_name = subject.name

                    # الحصول على المستوى
                    level = EducationalLevel.query.get(subject.level_id)
                    if level:
                        level_name = level.name

    # الحصول على المعلومات من قاعدة البيانات القديمة (إذا لم تكن موجودة في الجديدة)
    if level_name == 'غير محدد' and entry.competency_id:
        try:
            # محاولة الحصول على المعلومات من قاعدة البيانات القديمة
            # التحقق من وجود المعلومات في قاعدة البيانات القديمة
            competency_entry = LevelDataEntry.query.filter_by(id=entry.competency_id, entry_type='competency').first()
            if competency_entry:
                material_entry = LevelDataEntry.query.filter_by(id=competency_entry.parent_id, entry_type='material').first()
                if material_entry:
                    material_name = material_entry.name

                    domain_entry = LevelDataEntry.query.filter_by(id=material_entry.parent_id, entry_type='domain').first()
                    if domain_entry:
                        domain_name = domain_entry.name

                        subject_entry = LevelDataEntry.query.filter_by(id=domain_entry.parent_id, entry_type='subject').first()
                        if subject_entry:
                            subject_name = subject_entry.name

                            # الحصول على المستوى من قاعدة البيانات
                            db = LevelDatabase.query.filter_by(id=subject_entry.database_id).first()
                            if db and db.level_id:
                                level = EducationalLevel.query.get(db.level_id)
                                if level:
                                    level_name = level.name
        except Exception as e:
            # في حالة حدوث خطأ، نستمر بالقيم الافتراضية
            print(f"Error retrieving data from old database: {e}")
            pass

    # الحصول على نص الكفاءة (أولوية للاسم)
    competency_text = 'كفاءة غير معروفة'

    # محاولة الحصول على الكفاءة من النموذج الجديد أولاً
    if entry.competency:
        if entry.competency.name:
            competency_text = entry.competency.name
        elif entry.competency.description:
            competency_text = entry.competency.description
        else:
            competency_text = 'بدون اسم'
    # محاولة الحصول على الكفاءة من المادة المعرفية إذا كان material_id موجود
    elif entry.material_id:
        try:
            # الحصول على المادة المعرفية
            material_entry = LevelDataEntry.query.filter_by(id=entry.material_id, entry_type='material').first()
            if material_entry:
                # البحث عن الكفاءات المرتبطة بهذه المادة المعرفية
                competencies = LevelDataEntry.query.filter_by(
                    parent_id=entry.material_id,
                    entry_type='competency',
                    is_active=True
                ).all()

                if competencies:
                    # أخذ أول كفاءة متاحة (يمكن تحسين هذا لاحقاً)
                    first_competency = competencies[0]
                    if first_competency.name:
                        competency_text = first_competency.name
                    elif first_competency.description:
                        competency_text = first_competency.description
                    else:
                        competency_text = 'كفاءة مرتبطة بالمادة المعرفية'
                else:
                    competency_text = f'كفاءة مرتبطة بـ {material_entry.name}'
        except Exception as e:
            print(f"Error retrieving competency from material: {e}")
            competency_text = 'كفاءة غير معروفة'
    # محاولة الحصول على الكفاءة من النموذج الجديد LevelDataEntry باستخدام competency_id
    elif entry.competency_id:
        try:
            # أولاً، محاولة البحث المباشر
            competency_entry = LevelDataEntry.query.filter_by(id=entry.competency_id, entry_type='competency').first()
            if competency_entry:
                if competency_entry.name:
                    competency_text = competency_entry.name
                elif competency_entry.description:
                    competency_text = competency_entry.description
                else:
                    competency_text = 'بدون اسم'
            else:
                # إذا لم نجد الكفاءة، نأخذ كفاءة عشوائية من قاعدة البيانات كمثال
                sample_competency = LevelDataEntry.query.filter_by(entry_type='competency', is_active=True).first()
                if sample_competency:
                    if sample_competency.name:
                        competency_text = sample_competency.name
                    elif sample_competency.description:
                        competency_text = sample_competency.description
                    else:
                        competency_text = 'كفاءة نموذجية'
                else:
                    competency_text = 'كفاءة غير معروفة'
        except Exception as e:
            print(f"Error retrieving competency from LevelDataEntry: {e}")
            competency_text = 'كفاءة غير معروفة'
    else:
        # إذا لم توجد أي معلومات، نأخذ كفاءة نموذجية
        try:
            sample_competency = LevelDataEntry.query.filter_by(entry_type='competency', is_active=True).first()
            if sample_competency:
                if sample_competency.name:
                    competency_text = sample_competency.name
                elif sample_competency.description:
                    competency_text = sample_competency.description
                else:
                    competency_text = 'كفاءة نموذجية'
            else:
                competency_text = 'كفاءة غير معروفة'
        except Exception as e:
            print(f"Error retrieving sample competency: {e}")
            competency_text = 'كفاءة غير معروفة'

    # إعداد المعلومات للعرض في الصفحة
    lesson_info = {
        'level': level_name,
        'subject': subject_name,
        'domain': domain_name,
        'material': material_name,
        'competency': competency_text,
        'entry_id': entry_id
    }

    return render_template('prepare_lesson.html', lesson_info=lesson_info)

# إضافة أستاذ تحت الإشراف
@app.route('/inspector/add-teacher', methods=['POST'])
@login_required
def add_supervised_teacher():
    if current_user.role != Role.INSPECTOR:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    teacher_id = request.form.get('teacher_id')
    if not teacher_id:
        flash('يرجى اختيار أستاذ', 'danger')
        return redirect(url_for('inspector_dashboard'))

    # التحقق من وجود الأستاذ
    teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
    if not teacher:
        flash('الأستاذ غير موجود', 'danger')
        return redirect(url_for('inspector_dashboard'))

    # التحقق من أن الأستاذ ليس تحت إشراف المفتش بالفعل
    if teacher in current_user.supervised_teachers:
        flash('الأستاذ بالفعل تحت إشرافك', 'warning')
        return redirect(url_for('inspector_dashboard'))

    # إضافة الأستاذ تحت إشراف المفتش
    current_user.supervised_teachers.append(teacher)
    db.session.commit()

    # إرسال إشعار للأستاذ
    notification = InspectorTeacherNotification(
        sender_id=current_user.id,
        receiver_id=teacher.id,
        title='تم إضافتك تحت إشراف مفتش جديد',
        message=f'مرحباً! لقد تم إضافتك تحت إشراف المفتش {current_user.username}. يمكنك الآن مشاركة تقدمك والتواصل معه من خلال نظام الإشعارات.'
    )
    db.session.add(notification)
    db.session.commit()

    flash(f'تمت إضافة الأستاذ {teacher.username} تحت إشرافك بنجاح', 'success')
    return redirect(url_for('inspector_dashboard'))

# تعديل التقدم
@app.route('/progress/edit/<int:entry_id>', methods=['GET', 'POST'])
@login_required
def edit_progress(entry_id):
    # السماح للمعلمين والمفتشين بتعديل التقدم
    if current_user.role not in [Role.TEACHER, Role.INSPECTOR]:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    entry = ProgressEntry.query.get_or_404(entry_id)

    # التحقق من الصلاحيات
    if current_user.role == Role.TEACHER and entry.user_id != current_user.id:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))
    elif current_user.role == Role.INSPECTOR:
        # التحقق من أن المعلم تحت إشراف المفتش
        teacher = User.query.get(entry.user_id)
        if teacher not in current_user.supervised_teachers:
            flash('غير مصرح بالوصول', 'danger')
            return redirect(url_for('dashboard'))

    if request.method == 'POST':
        # تحديث حالة التقدم
        entry.status = request.form.get('status')
        entry.notes = request.form.get('notes')
        entry.date = datetime.strptime(request.form.get('date'), '%Y-%m-%d').date()
        # محاولة استخدام datetime.now مع timezone.utc
        try:
            entry.updated_at = datetime.now(timezone.utc)
        except (AttributeError, TypeError):
            # إذا فشلت، استخدم datetime.utcnow
            entry.updated_at = datetime.utcnow()

        # تحديث العلاقات إذا تم توفيرها
        if request.form.get('level_id'):
            entry.level_id = request.form.get('level_id')
        if request.form.get('subject_id'):
            entry.subject_id = request.form.get('subject_id')
        if request.form.get('domain_id'):
            entry.domain_id = request.form.get('domain_id')
        if request.form.get('material_id'):
            entry.material_id = request.form.get('material_id')
        if request.form.get('competency_id'):
            entry.competency_id = request.form.get('competency_id')

        # إذا لم يتم تحديد material_id ولكن تم تحديد domain_id، نحاول استنتاج material_id
        if not entry.material_id and entry.domain_id:
            material = LevelDataEntry.query.filter_by(
                parent_id=entry.domain_id,
                entry_type='material',
                is_active=True
            ).first()
            if material:
                entry.material_id = material.id

        db.session.commit()

        # إعادة حساب الإحصائيات بعد التحديث
        try:
            # تحديث أي سجلات أخرى قد تفتقر إلى material_id
            update_missing_material_ids(current_user.id)
        except Exception as e:
            print(f"Error updating missing material_ids after edit: {str(e)}")

        flash('تم تحديث التقدم بنجاح', 'success')

        # إعادة توجيه المستخدم حسب دوره
        if current_user.role == Role.TEACHER:
            return redirect(url_for('teacher_dashboard'))
        else:
            return redirect(url_for('inspector_dashboard'))

    # الحصول على البيانات اللازمة للقائمة المنسدلة
    levels = EducationalLevel.query.all()

    # الحصول على المواد والميادين والمواد المعرفية والكفاءات
    subjects = LevelDataEntry.query.filter_by(entry_type='subject').all()
    domains = LevelDataEntry.query.filter_by(entry_type='domain').all()
    materials = LevelDataEntry.query.filter_by(entry_type='material').all()
    competencies = Competency.query.all()

    return render_template('edit_progress.html',
                           entry=entry,
                           levels=levels,
                           subjects=subjects,
                           domains=domains,
                           materials=materials,
                           competencies=competencies)

# حذف التقدم
@app.route('/progress/delete/<int:entry_id>')
@login_required
def delete_progress(entry_id):
    if current_user.role != Role.TEACHER:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    entry = ProgressEntry.query.get_or_404(entry_id)

    # التحقق من أن المستخدم هو صاحب التقدم
    if entry.user_id != current_user.id:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    db.session.delete(entry)
    db.session.commit()

    flash('تم حذف التقدم بنجاح', 'success')
    return redirect(url_for('teacher_dashboard'))

# تسجيل التقدم للمادة المعرفية
@app.route('/progress/material/add', methods=['POST'])
@login_required
def add_material_progress():
    if current_user.role != Role.TEACHER:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    material_id = request.form.get('material_id')
    status = request.form.get('status', 'completed')
    date = request.form.get('date')
    notes = request.form.get('notes', '')

    if not material_id:
        flash('معرف المادة المعرفية مطلوب', 'danger')
        return redirect(url_for('teacher_dashboard'))

    # التحقق من وجود المادة المعرفية
    material = LevelDataEntry.query.filter_by(id=material_id, entry_type='material').first()
    if not material:
        flash('المادة المعرفية غير موجودة', 'danger')
        return redirect(url_for('teacher_dashboard'))

    # تحويل التاريخ
    try:
        if date:
            date = datetime.strptime(date, '%Y-%m-%d').date()
        else:
            date = datetime.now().date()
    except ValueError:
        date = datetime.now().date()

    # التحقق من الحالة
    if status not in ['completed', 'in_progress', 'planned']:
        status = 'completed'

    # التحقق من وجود سجل تقدم سابق لهذه المادة المعرفية
    existing_progress = ProgressEntry.query.filter_by(
        user_id=current_user.id,
        material_id=material_id
    ).first()

    if existing_progress:
        # تحديث السجل الموجود
        existing_progress.status = status
        existing_progress.date = date
        existing_progress.notes = notes
        existing_progress.updated_at = datetime.utcnow()
        flash('تم تحديث التقدم للمادة المعرفية بنجاح', 'success')
    else:
        # إنشاء سجل تقدم جديد
        new_progress = ProgressEntry(
            user_id=current_user.id,
            material_id=material_id,
            date=date,
            status=status,
            notes=notes
        )
        db.session.add(new_progress)
        flash('تم إضافة التقدم للمادة المعرفية بنجاح', 'success')

    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حفظ التقدم', 'danger')
        print(f"Error saving material progress: {str(e)}")

    return redirect(url_for('teacher_dashboard'))

# إعادة حساب الإحصائيات
@app.route('/recalculate_stats')
@login_required
def recalculate_stats():
    if current_user.role != Role.TEACHER:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # إعادة حساب الإحصائيات
        result = recalculate_teacher_stats(current_user.id)
        if result:
            flash('تم إعادة حساب الإحصائيات بنجاح', 'success')
        else:
            flash('حدث خطأ أثناء إعادة حساب الإحصائيات', 'warning')
    except Exception as e:
        print(f"Error in recalculate_stats: {str(e)}")
        flash('حدث خطأ أثناء إعادة حساب الإحصائيات', 'danger')

    return redirect(url_for('teacher_dashboard'))

# تعديل بيانات المستخدم
@app.route('/admin/users/edit', methods=['POST'])
@login_required
def edit_user():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    user_id = request.form.get('user_id')
    username = request.form.get('username')
    email = request.form.get('email')
    password = request.form.get('password')

    if not user_id or not username or not email:
        flash('الرجاء ملء جميع الحقول المطلوبة', 'danger')
        return redirect(url_for('admin_dashboard'))

    user = User.query.get_or_404(user_id)

    # التحقق من عدم وجود مستخدم آخر بنفس البريد الإلكتروني
    existing_user = User.query.filter(User.email == email, User.id != user_id).first()
    if existing_user:
        flash('البريد الإلكتروني مستخدم بالفعل', 'danger')
        return redirect(url_for('admin_dashboard'))

    user.username = username
    user.email = email

    if password:
        user.password = generate_password_hash(password)

    # إذا كان المستخدم أستاذاً وتم تحديد مشرف
    if user.role == Role.TEACHER:
        inspector_id = request.form.get('inspector_id')

        # إزالة جميع المشرفين الحاليين
        for inspector in user.inspectors:
            inspector.supervised_teachers.remove(user)

        # إضافة المشرف الجديد إذا تم تحديده
        if inspector_id:
            inspector = User.query.filter_by(id=inspector_id, role=Role.INSPECTOR).first()
            if inspector and user not in inspector.supervised_teachers:
                inspector.supervised_teachers.append(user)

    db.session.commit()

    flash('تم تحديث بيانات المستخدم بنجاح', 'success')
    return redirect(url_for('admin_dashboard'))

# تفعيل/تعطيل حساب مستخدم
@app.route('/admin/users/<int:user_id>/toggle-status')
@login_required
def toggle_user_status(user_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(user_id)

    # لا يمكن تعطيل أي حساب أدمن
    if user.role == Role.ADMIN:
        flash('لا يمكن تعطيل حسابات المديرين', 'danger')
        return redirect(url_for('admin_dashboard'))

    user.is_active = not user.is_active
    db.session.commit()

    status_msg = 'تفعيل' if user.is_active else 'تعطيل'
    flash(f'تم {status_msg} حساب {user.username} بنجاح', 'success')
    return redirect(url_for('admin_dashboard'))

# حذف حساب مستخدم
@app.route('/admin/users/<int:user_id>/delete')
@login_required
def delete_user(user_id):
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(user_id)

    # لا يمكن حذف حساب المدير الحالي
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'danger')
        return redirect(url_for('admin_dashboard'))

    # حذف العلاقات المرتبطة بالمستخدم
    if user.role == Role.INSPECTOR:
        # حذف العلاقات مع الأساتذة
        user.supervised_teachers = []

    # حذف الإشعارات
    InspectorTeacherNotification.query.filter_by(sender_id=user.id).delete()
    InspectorTeacherNotification.query.filter_by(receiver_id=user.id).delete()

    # حذف سجلات التقدم
    ProgressEntry.query.filter_by(user_id=user.id).delete()

    # حذف جدول المستخدم
    Schedule.query.filter_by(user_id=user.id).delete()

    # حذف المستخدم
    db.session.delete(user)
    db.session.commit()

    flash(f'تم حذف حساب {user.username} بنجاح', 'success')
    return redirect(url_for('admin_dashboard'))

# إنشاء قواعد بيانات المستويات الخمسة للتعليم الابتدائي
@app.route('/admin/create-primary-levels', methods=['POST'])
@login_required
def create_primary_levels():
    if current_user.role != Role.ADMIN:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # المستويات الخمسة للتعليم الابتدائي
        primary_levels = [
            'السنة الأولى ابتدائي',
            'السنة الثانية ابتدائي',
            'السنة الثالثة ابتدائي',
            'السنة الرابعة ابتدائي',
            'السنة الخامسة ابتدائي'
        ]

        created_levels = 0
        created_databases = 0

        for level_name in primary_levels:
            # البحث عن المستوى أو إنشاؤه
            level = EducationalLevel.query.filter_by(name=level_name).first()
            if not level:
                level = EducationalLevel(
                    name=level_name,
                    is_active=True,
                    database_prefix=f'primary_{level_name.split()[1]}'  # primary_الأولى، primary_الثانية، إلخ
                )
                db.session.add(level)
                db.session.flush()
                created_levels += 1

            # البحث عن قاعدة البيانات أو إنشاؤها
            level_db = LevelDatabase.query.filter_by(level_id=level.id).first()
            if not level_db:
                level_db = LevelDatabase(
                    level_id=level.id,
                    name=f'قاعدة بيانات {level_name}',
                    file_path=f'data/{level.database_prefix}_education.db',
                    is_active=True
                )
                db.session.add(level_db)
                db.session.flush()
                created_databases += 1
            else:
                # حذف جميع البيانات الموجودة في قاعدة البيانات
                LevelDataEntry.query.filter_by(database_id=level_db.id).delete()

            # إضافة المواد والميادين لهذا المستوى
            add_subjects_to_level(level_db.id)

        db.session.commit()

        # حساب إجمالي المواد والميادين المضافة
        total_subjects = 0
        total_domains = 0
        for level_name in primary_levels:
            level = EducationalLevel.query.filter_by(name=level_name).first()
            if level:
                level_db = LevelDatabase.query.filter_by(level_id=level.id).first()
                if level_db:
                    total_subjects += LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='subject').count()
                    total_domains += LevelDataEntry.query.filter_by(database_id=level_db.id, entry_type='domain').count()

        flash(f'✅ تم إنشاء/تحديث المستويات الخمسة للتعليم الابتدائي بنجاح! تم إضافة {total_subjects} مادة و {total_domains} ميدان إجمالي (13 مادة و 65 ميدان لكل مستوى)', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'❌ حدث خطأ أثناء إنشاء قواعد البيانات: {str(e)}', 'danger')
        print(f"Error in create_primary_levels: {str(e)}")  # للتشخيص
        import traceback
        traceback.print_exc()  # للتشخيص

    return redirect(url_for('manage_databases'))

def add_subjects_to_level(database_id):
    """إضافة المواد والميادين الثابتة لمستوى تعليمي"""
    # تعريف المواد والميادين
    subjects_domains = {
            'اللغة العربية': [
                'فهم المنطوق',
                'التعبير الشفهي',
                'فهم المكتوب (ألعاب قرائية)',
                'التعبير الشفهي ( صيغ )',
                'التعبير الشفهي ( إنتاج)',
                'التعبير الكتابي (أركب)',
                'التعبير الكتابي (إنتاج كتابي)',
                'استكشاف الحرف',
                'كتابة الحرف',
                'إملاء',
                'التراكيب النحوية',
                'الصرف',
                'قراءة اجمالية',
                'قراءة (اداء و فهم)',
                'محفوظات',
                'المشاريع',
                'تطبيقات اللغة',
                'تصحيح التعبير الكتابي',
                'معالجة اللغة العربية',
                'تقويم فصلي في اللغة العربية'
            ],
            'الرياضيات': [
                'الاعداد و الحساب',
                'الفضاء و الهندسة',
                'المقادير و القياس',
                'تنظيم المعطيات',
                'تطبيقات الرياضيات',
                'معالجة الرياضيات',
                'تقويم فصلي في الرياضيات'
            ],
            'التربية الإسلامية': [
                'القرآن والحديث',
                'تهذيب السلوك',
                'مبادئ في العقيدة',
                'مبادئ في السيرة',
                'استعراض النص الشرعي',
                'تقويم فصلي في التربية الإسلامية'
            ],
            'الفرنسية': [
                'حصص الفرنسية'
            ],
            'التربية العلمية': [
                'الإنسان و الصحة',
                'الإنسان و المحيط',
                'المعلمة في الفضاء و الزمن',
                'المادة و علم الأشياء',
                'تقويم فصلي في التربية العلمية'
            ],
            'التربية المدنية': [
                'الحياة الجماعية',
                'الحياة المدنية',
                'الحياة الديمقراطية و المؤسسات',
                'تقويم فصلي في التربية المدنية'
            ],
            'التربية الفنية /التشكيلية': [
                'الرسم و التلوين',
                'فن التصميم',
                'النشيد و الأغنية التربوية',
                'التذوق الموسيقي و الاستماع',
                'القواعد الموسيقية و النظريات',
                'تقويم فصلي التربية الفنية'
            ],
            'التاريخ': [
                'أدوات و مفاهيم مادة التاريخ',
                'التاريخ العام',
                'التاريخ الوطني',
                'تقويم فصلي في التاريخ و الغرافيا'
            ],
            'الجغرافيا': [
                'أدوات و مفاهيم مادة الجغرافيا',
                'السكان و التنمية',
                'السكان و البيئة',
                'تقويم فصلي  في التاريخ و الجغرافيا'
            ],
            'التربية البدنية': [
                'الوضعيات و التنقلات',
                'الحركات القاعدية',
                'الهيكلة و البناء',
                'تقويم فصلي في التربية البدنية'
            ],
            'حفظ القرآن': [
                'استعراض السورة',
                'استعراض الحديث'
            ],
            'الأمازيغية': [
                'ⵜⴰⵎⴰⵣⵉⴳⵀ'
            ],
            'الإنجليزية': [
                'English_lesson'
            ]
    }

    # إضافة المواد والميادين
    for subject_name, domains in subjects_domains.items():
        # إضافة المادة
        subject_entry = LevelDataEntry(
            database_id=database_id,
            entry_type='subject',
            name=subject_name,
            description=f'مادة {subject_name}',
            is_active=True,
            order_num=list(subjects_domains.keys()).index(subject_name) + 1
        )
        db.session.add(subject_entry)
        db.session.flush()  # للحصول على معرف المادة

        # إضافة الميادين للمادة
        for i, domain_name in enumerate(domains):
            domain_entry = LevelDataEntry(
                database_id=database_id,
                entry_type='domain',
                parent_id=subject_entry.id,
                name=domain_name,
                description=f'ميدان {domain_name} في مادة {subject_name}',
                is_active=True,
                order_num=i + 1
            )
            db.session.add(domain_entry)

# عرض التقدم التفصيلي للأستاذ (آخر 5 دروس مكتملة)
@app.route('/api/teacher_detailed_progress/<int:teacher_id>')
@login_required
def get_teacher_detailed_progress(teacher_id):
    if current_user.role != Role.INSPECTOR:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403

    # التحقق من أن الأستاذ تحت إشراف المفتش الحالي
    teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
    if not teacher or teacher not in current_user.supervised_teachers:
        return jsonify({'error': 'الأستاذ غير موجود أو ليس تحت إشرافك'}), 404

    try:
        # الحصول على آخر 5 دروس مكتملة
        completed_lessons = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='completed'
        ).order_by(ProgressEntry.date.desc()).limit(5).all()

        lessons_data = []
        for progress in completed_lessons:
            lesson_info = {
                'id': progress.id,
                'date': progress.date.strftime('%Y-%m-%d') if progress.date else 'غير محدد',
                'notes': progress.notes or 'لا توجد ملاحظات',
                'level': 'غير محدد',
                'subject': 'غير محدد',
                'domain': 'غير محدد',
                'material': 'غير محدد',
                'competency': 'غير محدد'
            }

            # محاولة الحصول على تفاصيل الدرس من النظام الجديد
            if progress.competency_id:
                competency = LevelDataEntry.query.filter_by(id=progress.competency_id, entry_type='competency').first()
                if competency:
                    lesson_info['competency'] = competency.name

                    # الحصول على المادة المعرفية
                    if competency.parent_id:
                        material = LevelDataEntry.query.filter_by(id=competency.parent_id, entry_type='material').first()
                        if material:
                            lesson_info['material'] = material.name

                            # الحصول على الميدان
                            if material.parent_id:
                                domain = LevelDataEntry.query.filter_by(id=material.parent_id, entry_type='domain').first()
                                if domain:
                                    lesson_info['domain'] = domain.name

                                    # الحصول على المادة الدراسية
                                    if domain.parent_id:
                                        subject = LevelDataEntry.query.filter_by(id=domain.parent_id, entry_type='subject').first()
                                        if subject:
                                            lesson_info['subject'] = subject.name

                                            # الحصول على المستوى التعليمي
                                            db_entry = LevelDatabase.query.filter_by(id=subject.database_id).first()
                                            if db_entry and db_entry.level_id:
                                                level = EducationalLevel.query.get(db_entry.level_id)
                                                if level:
                                                    lesson_info['level'] = level.name

            lessons_data.append(lesson_info)

        # إحصائيات عامة
        total_completed = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='completed'
        ).count()

        total_in_progress = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='in_progress'
        ).count()

        total_planned = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='planned'
        ).count()

        return jsonify({
            'success': True,
            'teacher_name': teacher.username,
            'recent_lessons': lessons_data,
            'stats': {
                'completed': total_completed,
                'in_progress': total_in_progress,
                'planned': total_planned,
                'total': total_completed + total_in_progress + total_planned
            }
        })

    except Exception as e:
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500

# عرض الملف الشخصي للأستاذ (للقراءة فقط)
@app.route('/api/teacher_profile/<int:teacher_id>')
@login_required
def get_teacher_profile_readonly(teacher_id):
    if current_user.role != Role.INSPECTOR:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403

    # التحقق من أن الأستاذ تحت إشراف المفتش الحالي
    teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
    if not teacher or teacher not in current_user.supervised_teachers:
        return jsonify({'error': 'الأستاذ غير موجود أو ليس تحت إشرافك'}), 404

    try:
        # معلومات الأستاذ
        profile_data = {
            'id': teacher.id,
            'username': teacher.username,
            'email': teacher.email,
            'full_name': getattr(teacher, 'full_name', 'غير محدد'),
            'phone': getattr(teacher, 'phone', 'غير محدد'),
            'address': getattr(teacher, 'address', 'غير محدد'),
            'created_at': teacher.created_at.strftime('%Y-%m-%d') if teacher.created_at else 'غير محدد',
            'updated_at': teacher.updated_at.strftime('%Y-%m-%d %H:%M') if teacher.updated_at else 'غير محدد',
            'last_login': getattr(teacher, 'last_login', 'لم يسجل دخول بعد'),
            'is_active': teacher.is_active
        }

        # إحصائيات التقدم
        completed_count = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='completed'
        ).count()

        in_progress_count = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='in_progress'
        ).count()

        planned_count = ProgressEntry.query.filter_by(
            user_id=teacher_id,
            status='planned'
        ).count()

        profile_data['progress_stats'] = {
            'completed': completed_count,
            'in_progress': in_progress_count,
            'planned': planned_count,
            'total': completed_count + in_progress_count + planned_count
        }

        # حساب نسبة الإنجاز
        if profile_data['progress_stats']['total'] > 0:
            profile_data['completion_rate'] = round(
                (completed_count / profile_data['progress_stats']['total']) * 100, 2
            )
        else:
            profile_data['completion_rate'] = 0

        return jsonify({
            'success': True,
            'profile': profile_data
        })

    except Exception as e:
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500

# إزالة أستاذ من الإشراف
@app.route('/api/remove_teacher_supervision/<int:teacher_id>', methods=['POST'])
@login_required
def remove_teacher_supervision(teacher_id):
    if current_user.role != Role.INSPECTOR:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403

    try:
        # التحقق من أن الأستاذ تحت إشراف المفتش الحالي
        teacher = User.query.filter_by(id=teacher_id, role=Role.TEACHER).first()
        if not teacher or teacher not in current_user.supervised_teachers:
            return jsonify({'error': 'الأستاذ غير موجود أو ليس تحت إشرافك'}), 404

        # إزالة الإشراف
        current_user.supervised_teachers.remove(teacher)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'تم إزالة الأستاذ {teacher.username} من الإشراف بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500

# عرض الملف الشخصي للمستخدم (للأدمن ومدير المستخدمين)
@app.route('/profile/view/<int:user_id>')
@login_required
def view_user_profile(user_id):
    # التحقق من الصلاحيات
    if current_user.role not in [Role.ADMIN, Role.USER_MANAGER]:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    # الحصول على بيانات المستخدم
    user = User.query.get_or_404(user_id)

    # إحصائيات إضافية للمستخدم (إذا كان معلماً أو مفتشاً)
    stats = {}
    if user.role == Role.TEACHER:
        # إحصائيات المعلم
        stats['total_schedules'] = Schedule.query.filter_by(user_id=user.id).count()
        stats['total_progress'] = ProgressEntry.query.filter_by(user_id=user.id).count()
    elif user.role == Role.INSPECTOR:
        # إحصائيات المفتش
        stats['assigned_teachers'] = len(user.supervised_teachers.all())
        stats['total_notifications'] = InspectorTeacherNotification.query.filter_by(sender_id=user.id).count()

    return render_template('user_profile.html', user=user, stats=stats)

# قائمة المستخدمين مع إمكانية البحث والفلترة
@app.route('/users/list')
@login_required
def users_list():
    # التحقق من الصلاحيات
    if current_user.role not in [Role.ADMIN, Role.USER_MANAGER]:
        flash('غير مصرح بالوصول', 'danger')
        return redirect(url_for('dashboard'))

    # معاملات البحث والفلترة
    search = request.args.get('search', '')
    role_filter = request.args.get('role', '')
    wilaya_filter = request.args.get('wilaya', '')
    status_filter = request.args.get('status', '')
    page = request.args.get('page', 1, type=int)

    # بناء الاستعلام
    query = User.query

    # البحث بالاسم أو رقم الهاتف
    if search:
        query = query.filter(
            (User.username.contains(search)) |
            (User.phone_number.contains(search))
        )

    # فلترة حسب الدور
    if role_filter:
        query = query.filter(User.role == role_filter)

    # فلترة حسب الولاية
    if wilaya_filter:
        query = query.filter(User.wilaya_code == wilaya_filter)

    # فلترة حسب الحالة
    if status_filter == 'active':
        query = query.filter(User._is_active == True)
    elif status_filter == 'inactive':
        query = query.filter(User._is_active == False)

    # ترتيب النتائج
    query = query.order_by(User.username)

    # تقسيم النتائج إلى صفحات
    users = query.paginate(
        page=page,
        per_page=20,
        error_out=False
    )

    # إحصائيات
    total_users = User.query.count()
    active_users = User.query.filter_by(_is_active=True).count()
    inactive_users = total_users - active_users

    stats = {
        'total': total_users,
        'active': active_users,
        'inactive': inactive_users,
        'teachers': User.query.filter_by(role=Role.TEACHER).count(),
        'inspectors': User.query.filter_by(role=Role.INSPECTOR).count(),
        'admins': User.query.filter_by(role=Role.ADMIN).count(),
        'user_managers': User.query.filter_by(role=Role.USER_MANAGER).count()
    }

    return render_template('users_list.html',
                         users=users,
                         stats=stats,
                         search=search,
                         role_filter=role_filter,
                         wilaya_filter=wilaya_filter,
                         status_filter=status_filter,
                         wilayas=ALGERIAN_WILAYAS)

# تصدير التقدمات إلى Excel
@app.route('/export_progress_excel', methods=['POST'])
@login_required
def export_progress_excel():
    """تصدير التقدمات المحددة إلى ملف Excel"""
    try:
        if current_user.role != Role.TEACHER:
            flash('غير مصرح بالوصول', 'danger')
            return redirect(url_for('dashboard'))

        # الحصول على التاريخ المحدد
        selected_date = request.form.get('export_date')
        if not selected_date:
            flash('يرجى تحديد تاريخ للتصدير', 'warning')
            return redirect(url_for('teacher_dashboard'))

        # تحويل التاريخ
        try:
            export_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
        except ValueError:
            flash('تاريخ غير صحيح', 'danger')
            return redirect(url_for('teacher_dashboard'))

        # الحصول على التقدمات للتاريخ المحدد
        progress_entries = ProgressEntry.query.filter_by(
            user_id=current_user.id,
            date=export_date
        ).order_by(ProgressEntry.id.desc()).all()

        if not progress_entries:
            flash(f'لا توجد تقدمات في التاريخ {selected_date}', 'info')
            return redirect(url_for('teacher_dashboard'))

        # إعداد البيانات للتصدير
        data = []
        for entry in progress_entries:
            # الحصول على معلومات الكفاءة
            competency_name = 'غير محدد'
            if entry.competency_id:
                # البحث في LevelDataEntry أولاً
                competency = LevelDataEntry.query.filter_by(
                    id=entry.competency_id,
                    entry_type='competency'
                ).first()
                if competency and competency.name:
                    competency_name = competency.name
                else:
                    # البحث في Competency القديم
                    old_competency = Competency.query.get(entry.competency_id)
                    if old_competency:
                        competency_name = old_competency.name or old_competency.description or 'غير محدد'
            elif entry.material_id and entry.material:
                competency_name = f"تقدم في {entry.material.name}"

            # إعداد حالة التقدم
            status_text = {
                'completed': 'مكتمل',
                'in_progress': 'قيد التنفيذ',
                'planned': 'مخطط'
            }.get(entry.status, entry.status)

            data.append({
                'التاريخ': entry.date.strftime('%Y-%m-%d') if entry.date else '',
                'المستوى': entry.level.name if entry.level else 'غير محدد',
                'المادة الدراسية': entry.subject.name if entry.subject else 'غير محدد',
                'الميدان': entry.domain.name if entry.domain else 'غير محدد',
                'المادة المعرفية': entry.material.name if entry.material else 'غير محدد',
                'الكفاءة المستهدفة': competency_name,
                'الحالة': status_text,
                'ملاحظات': entry.notes or ''
            })

        # إنشاء DataFrame
        df = pd.DataFrame(data)
        print(f"تم إنشاء DataFrame بنجاح مع {len(df)} صف")

        # إنشاء ملف Excel في الذاكرة بطريقة بسيطة
        output = BytesIO()

        try:
            # محاولة استخدام openpyxl مع تنسيق الأعمدة
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='التقدمات')

                # تنسيق عرض الأعمدة
                try:
                    worksheet = writer.sheets['التقدمات']

                    # تحديد عرض الأعمدة حسب المحتوى
                    column_widths = {
                        'A': 15,  # التاريخ
                        'B': 18,  # المستوى
                        'C': 20,  # المادة الدراسية
                        'D': 25,  # الميدان
                        'E': 30,  # المادة المعرفية
                        'F': 60,  # الكفاءة المستهدفة (أطول بكثير)
                        'G': 15,  # الحالة
                        'H': 40   # ملاحظات
                    }

                    for column_letter, width in column_widths.items():
                        worksheet.column_dimensions[column_letter].width = width

                    # تحسين إضافي: تعديل عرض الأعمدة حسب المحتوى الفعلي
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter

                        for cell in column:
                            try:
                                if cell.value:
                                    cell_length = len(str(cell.value))
                                    if cell_length > max_length:
                                        max_length = cell_length
                            except:
                                pass

                        # تطبيق عرض محسوب مع حد أدنى وأقصى
                        if column_letter == 'F':  # عمود الكفاءة المستهدفة
                            adjusted_width = min(max(max_length + 5, 40), 80)
                        else:
                            adjusted_width = min(max(max_length + 2, 12), 50)

                        worksheet.column_dimensions[column_letter].width = adjusted_width

                    # تنسيق بسيط للرأس
                    try:
                        for cell in worksheet[1]:
                            if OPENPYXL_AVAILABLE:
                                cell.font = Font(bold=True, size=12)
                                cell.alignment = Alignment(horizontal='center', vertical='center')
                    except Exception as header_error:
                        print(f"تحذير: لم يتم تنسيق الرأس: {header_error}")

                    print("تم تطبيق تنسيق عرض الأعمدة بنجاح")
                except Exception as format_error:
                    print(f"تحذير: لم يتم تطبيق تنسيق الأعمدة: {format_error}")

        except Exception as e:
            print(f"خطأ مع openpyxl: {e}")
            try:
                # محاولة بدون تحديد engine
                output = BytesIO()
                df.to_excel(output, index=False, sheet_name='التقدمات')
            except Exception as e2:
                print(f"خطأ في إنشاء ملف Excel: {e2}")
                # كحل أخير، تصدير كـ CSV
                try:
                    output = BytesIO()
                    csv_data = df.to_csv(index=False, encoding='utf-8-sig')
                    output.write(csv_data.encode('utf-8-sig'))
                    output.seek(0)

                    filename = f'progress_export_{selected_date}.csv'
                    return send_file(
                        output,
                        mimetype='text/csv',
                        as_attachment=True,
                        download_name=filename
                    )
                except Exception as e3:
                    print(f"خطأ في إنشاء ملف CSV: {e3}")
                    flash(f'خطأ في إنشاء الملف: {str(e3)}', 'danger')
                    return redirect(url_for('teacher_dashboard'))

        output.seek(0)

        # إنشاء اسم الملف
        filename = f"تقدمات_{current_user.username}_{selected_date}.xlsx"

        # إرسال الملف
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        print(f"Error in export_progress_excel: {str(e)}")
        flash('حدث خطأ أثناء تصدير البيانات', 'danger')
        return redirect(url_for('teacher_dashboard'))

# طباعة التقدمات
@app.route('/print_progress', methods=['POST'])
@login_required
def print_progress():
    """إعداد صفحة طباعة للتقدمات المحددة"""
    try:
        if current_user.role != Role.TEACHER:
            flash('غير مصرح بالوصول', 'danger')
            return redirect(url_for('dashboard'))

        # الحصول على التاريخ المحدد
        selected_date = request.form.get('print_date')
        if not selected_date:
            flash('يرجى تحديد تاريخ للطباعة', 'warning')
            return redirect(url_for('teacher_dashboard'))

        # تحويل التاريخ
        try:
            print_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
        except ValueError:
            flash('تاريخ غير صحيح', 'danger')
            return redirect(url_for('teacher_dashboard'))

        # الحصول على التقدمات للتاريخ المحدد
        progress_entries = ProgressEntry.query.filter_by(
            user_id=current_user.id,
            date=print_date
        ).order_by(ProgressEntry.id.desc()).all()

        if not progress_entries:
            flash(f'لا توجد تقدمات في التاريخ {selected_date}', 'info')
            return redirect(url_for('teacher_dashboard'))

        # إعداد البيانات للطباعة
        print_data = []
        for entry in progress_entries:
            # الحصول على معلومات الكفاءة
            competency_name = 'غير محدد'
            if entry.competency_id:
                # البحث في LevelDataEntry أولاً
                competency = LevelDataEntry.query.filter_by(
                    id=entry.competency_id,
                    entry_type='competency'
                ).first()
                if competency and competency.name:
                    competency_name = competency.name
                else:
                    # البحث في Competency القديم
                    old_competency = Competency.query.get(entry.competency_id)
                    if old_competency:
                        competency_name = old_competency.name or old_competency.description or 'غير محدد'
            elif entry.material_id and entry.material:
                competency_name = f"تقدم في {entry.material.name}"

            # إعداد حالة التقدم
            status_text = {
                'completed': 'مكتمل',
                'in_progress': 'قيد التنفيذ',
                'planned': 'مخطط'
            }.get(entry.status, entry.status)

            print_data.append({
                'entry': entry,
                'competency_name': competency_name,
                'status_text': status_text
            })

        return render_template('print_progress.html',
                             progress_data=print_data,
                             selected_date=selected_date,
                             teacher_name=current_user.username,
                             total_entries=len(print_data))

    except Exception as e:
        print(f"Error in print_progress: {str(e)}")
        flash('حدث خطأ أثناء إعداد الطباعة', 'danger')
        return redirect(url_for('teacher_dashboard'))

if __name__ == '__main__':
    create_sample_data()
    app.run(debug=True)
